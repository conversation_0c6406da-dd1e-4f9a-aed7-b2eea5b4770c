package com.example.clean0522

import com.example.clean0522.ui.navigation.BottomNavItem
import com.example.clean0522.ui.navigation.Screen
import org.junit.Test
import org.junit.Assert.*

/**
 * Test for bottom navigation bar functionality
 */
class BottomNavBarTest {

    @Test
    fun testBottomNavItems_correctRoutes() {
        // Test that all bottom nav items have correct routes
        assertEquals("Files route should match", Screen.Files.route, BottomNavItem.Files.route)
        assertEquals("Antivirus route should match", Screen.Antivirus.route, BottomNavItem.Antivirus.route)
        assertEquals("More route should match", Screen.More.route, BottomNavItem.More.route)
    }

    @Test
    fun testBottomNavItems_correctTitles() {
        // Test that all bottom nav items have correct titles
        assertEquals("Files title should be correct", "Files", BottomNavItem.Files.title)
        assertEquals("Antivirus title should be correct", "Antivirus", BottomNavItem.Antivirus.title)
        assertEquals("More title should be correct", "More", BottomNavItem.More.title)
    }

    @Test
    fun testBottomNavItems_correctIcons() {
        // Test that all bottom nav items have correct icon resources
        assertEquals("Files selected icon should be correct", 
            R.mipmap.tab_files_s, BottomNavItem.Files.selectedIcon)
        assertEquals("Files unselected icon should be correct", 
            R.mipmap.tab_files_n, BottomNavItem.Files.unselectedIcon)
            
        assertEquals("Antivirus selected icon should be correct", 
            R.mipmap.tab_antivirus_s, BottomNavItem.Antivirus.selectedIcon)
        assertEquals("Antivirus unselected icon should be correct", 
            R.mipmap.tab_antivirus_n, BottomNavItem.Antivirus.unselectedIcon)
            
        assertEquals("More selected icon should be correct", 
            R.mipmap.tab_more_s, BottomNavItem.More.selectedIcon)
        assertEquals("More unselected icon should be correct", 
            R.mipmap.tab_more_n, BottomNavItem.More.unselectedIcon)
    }

    @Test
    fun testIconSelection_logic() {
        // Test icon selection logic
        val filesItem = BottomNavItem.Files
        val currentRoute = Screen.Files.route
        
        // When selected
        val isSelected = currentRoute == filesItem.route
        val expectedIcon = if (isSelected) filesItem.selectedIcon else filesItem.unselectedIcon
        
        assertTrue("Should be selected when routes match", isSelected)
        assertEquals("Should use selected icon when selected", 
            R.mipmap.tab_files_s, expectedIcon)
    }

    @Test
    fun testIconSelection_unselected() {
        // Test icon selection when not selected
        val filesItem = BottomNavItem.Files
        val currentRoute = Screen.Antivirus.route // Different route
        
        // When not selected
        val isSelected = currentRoute == filesItem.route
        val expectedIcon = if (isSelected) filesItem.selectedIcon else filesItem.unselectedIcon
        
        assertFalse("Should not be selected when routes don't match", isSelected)
        assertEquals("Should use unselected icon when not selected", 
            R.mipmap.tab_files_n, expectedIcon)
    }

    @Test
    fun testColorSelection_logic() {
        // Test color selection logic
        val isSelected = true
        val expectedTextColor = if (isSelected) R.color.text_blue else R.color.text_gray
        
        assertEquals("Should use blue color when selected", 
            R.color.text_blue, expectedTextColor)
        
        val isUnselected = false
        val expectedUnselectedColor = if (isUnselected) R.color.text_blue else R.color.text_gray
        
        assertEquals("Should use gray color when unselected", 
            R.color.text_gray, expectedUnselectedColor)
    }
}
