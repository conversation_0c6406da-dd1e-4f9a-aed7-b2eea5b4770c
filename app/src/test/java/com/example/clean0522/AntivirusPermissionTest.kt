package com.example.clean0522

import android.content.Context
import com.example.clean0522.data.manager.AntivirusPreferencesManager
import com.example.clean0522.utils.PermissionUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.Test
import org.junit.Assert.*

/**
 * Test for antivirus permission logic
 */
class AntivirusPermissionTest {

    @Test
    fun testAntivirusPermissionLogic_bothConditionsMet() {
        val context = mockk<Context>()
        val preferencesManager = mockk<AntivirusPreferencesManager>()
        
        // Mock both conditions as true
        every { preferencesManager.isPrivacyAgreementAccepted() } returns true
        
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasStoragePermission(context) } returns true
        
        // Test logic: both conditions met should allow direct scan
        val hasPrivacy = preferencesManager.isPrivacyAgreementAccepted()
        val hasPermission = PermissionUtils.hasStoragePermission(context)
        
        assertTrue("Should have privacy agreement", hasPrivacy)
        assertTrue("Should have storage permission", hasPermission)
        assertTrue("Should allow direct scan", hasPrivacy && hasPermission)
    }

    @Test
    fun testAntivirusPermissionLogic_noPrivacyAgreement() {
        val context = mockk<Context>()
        val preferencesManager = mockk<AntivirusPreferencesManager>()
        
        // Mock no privacy agreement
        every { preferencesManager.isPrivacyAgreementAccepted() } returns false
        
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasStoragePermission(context) } returns true
        
        // Test logic: no privacy agreement should show privacy dialog first
        val hasPrivacy = preferencesManager.isPrivacyAgreementAccepted()
        val hasPermission = PermissionUtils.hasStoragePermission(context)
        
        assertFalse("Should not have privacy agreement", hasPrivacy)
        assertTrue("Should have storage permission", hasPermission)
        assertFalse("Should not allow direct scan", hasPrivacy && hasPermission)
    }

    @Test
    fun testAntivirusPermissionLogic_noStoragePermission() {
        val context = mockk<Context>()
        val preferencesManager = mockk<AntivirusPreferencesManager>()
        
        // Mock has privacy but no storage permission
        every { preferencesManager.isPrivacyAgreementAccepted() } returns true
        
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasStoragePermission(context) } returns false
        
        // Test logic: has privacy but no permission should check permission
        val hasPrivacy = preferencesManager.isPrivacyAgreementAccepted()
        val hasPermission = PermissionUtils.hasStoragePermission(context)
        
        assertTrue("Should have privacy agreement", hasPrivacy)
        assertFalse("Should not have storage permission", hasPermission)
        assertFalse("Should not allow direct scan", hasPrivacy && hasPermission)
    }
}
