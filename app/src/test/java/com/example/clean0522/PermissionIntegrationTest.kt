package com.example.clean0522

import android.content.Context
import com.example.clean0522.utils.PermissionUtils
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.Assert.*

/**
 * Test for permission integration
 */
class PermissionIntegrationTest {

    @Test
    fun testPermissionUtils_hasStoragePermission() {
        val context = mockk<Context>()
        
        // Test permission check logic exists
        assertNotNull(PermissionUtils.hasStoragePermission(context))
    }

    @Test
    fun testPermissionUtils_getStoragePermissionIntent() {
        val context = mockk<Context>()
        every { context.packageName } returns "com.example.clean0522"
        
        // Test intent creation logic exists
        val intent = PermissionUtils.getStoragePermissionIntent(context)
        assertNotNull(intent)
    }
}
