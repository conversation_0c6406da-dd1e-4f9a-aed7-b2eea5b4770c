package com.example.clean0522

import android.content.Context
import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.utils.PermissionUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.Test
import org.junit.Assert.*

/**
 * Test for notification permission logic
 */
class NotificationPermissionTest {

    @Test
    fun testNotificationPermissionLogic_hasPermissionAndEnabled() {
        val context = mockk<Context>()
        val preferencesManager = mockk<NotificationPreferencesManager>()
        
        // Mock both permission and preference as true
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasNotificationPermission(context) } returns true
        every { preferencesManager.isNotificationEnabled() } returns true
        
        // Test logic: both conditions met should show enabled
        val hasPermission = PermissionUtils.hasNotificationPermission(context)
        val preferenceEnabled = preferencesManager.isNotificationEnabled()
        val effectiveEnabled = hasPermission && preferenceEnabled
        
        assertTrue("Should have notification permission", hasPermission)
        assertTrue("Should have preference enabled", preferenceEnabled)
        assertTrue("Should show as enabled", effectiveEnabled)
    }

    @Test
    fun testNotificationPermissionLogic_noPermission() {
        val context = mockk<Context>()
        val preferencesManager = mockk<NotificationPreferencesManager>()
        
        // Mock no permission but preference enabled
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasNotificationPermission(context) } returns false
        every { preferencesManager.isNotificationEnabled() } returns true
        
        // Test logic: no permission should show disabled regardless of preference
        val hasPermission = PermissionUtils.hasNotificationPermission(context)
        val preferenceEnabled = preferencesManager.isNotificationEnabled()
        val effectiveEnabled = hasPermission && preferenceEnabled
        
        assertFalse("Should not have notification permission", hasPermission)
        assertTrue("Should have preference enabled", preferenceEnabled)
        assertFalse("Should show as disabled due to no permission", effectiveEnabled)
    }

    @Test
    fun testNotificationPermissionLogic_hasPermissionButDisabled() {
        val context = mockk<Context>()
        val preferencesManager = mockk<NotificationPreferencesManager>()
        
        // Mock has permission but preference disabled
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasNotificationPermission(context) } returns true
        every { preferencesManager.isNotificationEnabled() } returns false
        
        // Test logic: has permission but preference disabled should show disabled
        val hasPermission = PermissionUtils.hasNotificationPermission(context)
        val preferenceEnabled = preferencesManager.isNotificationEnabled()
        val effectiveEnabled = hasPermission && preferenceEnabled
        
        assertTrue("Should have notification permission", hasPermission)
        assertFalse("Should have preference disabled", preferenceEnabled)
        assertFalse("Should show as disabled due to preference", effectiveEnabled)
    }
}
