package com.example.clean0522.domain.usecase

import com.example.clean0522.domain.model.StorageInfo
import com.example.clean0522.domain.repository.StorageRepository
import kotlinx.coroutines.flow.Flow

/**
 * Use case for getting storage information
 */
class GetStorageInfoUseCase(
    private val storageRepository: StorageRepository
) {
    operator fun invoke(): Flow<StorageInfo> {
        return storageRepository.getStorageInfo()
    }
}
