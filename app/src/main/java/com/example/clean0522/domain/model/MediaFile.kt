package com.example.clean0522.domain.model

import java.io.File

/**
 * Represents a media file with metadata
 */
data class MediaFile(
    val file: File,
    val name: String = file.name,
    val path: String = file.absolutePath,
    val size: Long = file.length(),
    val lastModified: Long = file.lastModified(),
    val mimeType: String = "",
    val extension: String = file.extension.lowercase(),
    val dateAdded: Long = 0L,
    val dateTaken: Long = 0L,
    val duration: Long = 0L, // For videos and audios
    val width: Int = 0, // For images and videos
    val height: Int = 0, // For images and videos
    val thumbnailPath: String? = null
) {
    val isImage: Boolean
        get() = mimeType.startsWith("image/") || 
                listOf("jpg", "jpeg", "png", "gif", "bmp", "webp").contains(extension)
    
    val isVideo: Boolean
        get() = mimeType.startsWith("video/") || 
                listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "3gp").contains(extension)
    
    val isAudio: Boolean
        get() = mimeType.startsWith("audio/") || 
                listOf("mp3", "wav", "ogg", "m4a", "aac", "flac").contains(extension)
    
    val isDocument: Boolean
        get() = listOf("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt").contains(extension)
    
    val isArchive: Boolean
        get() = listOf("zip", "rar", "7z", "tar", "gz").contains(extension)
    
    val isApk: Boolean
        get() = extension == "apk"
    
    val documentType: DocumentType
        get() = when (extension) {
            "pdf" -> DocumentType.PDF
            "doc", "docx" -> DocumentType.WORD
            "ppt", "pptx" -> DocumentType.PPT
            "xls", "xlsx" -> DocumentType.EXCEL
            "txt" -> DocumentType.TXT
            else -> DocumentType.OTHER
        }
}

/**
 * Document type enumeration
 */
enum class DocumentType {
    ALL, PDF, WORD, PPT, EXCEL, TXT, OTHER
}

/**
 * Grouped media files by date
 */
data class GroupedMediaFiles(
    val date: String,
    val files: List<MediaFile>
)
