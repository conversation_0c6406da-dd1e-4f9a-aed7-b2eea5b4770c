package com.example.clean0522.domain.model

/**
 * Represents information about device storage
 */
data class StorageInfo(
    val totalSpace: Long,
    val usedSpace: Long,
    val freeSpace: Long,
    val usagePercentage: Float
) {
    companion object {
        val EMPTY = StorageInfo(
            totalSpace = 0L,
            usedSpace = 0L,
            freeSpace = 0L,
            usagePercentage = 0f
        )
    }
}
