package com.example.clean0522.domain.model

import com.example.clean0522.utils.FileUtils
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 表示文件系统中的文件或目录
 */
data class FileItem(
    val file: File,
    val name: String = file.name,
    val isDirectory: Boolean = file.isDirectory,
    val lastModified: Long = file.lastModified(),
    val path: String = file.absolutePath,
    val extension: String = if (!isDirectory) file.extension else "",
    val mimeType: String = if (!isDirectory) FileUtils.getMimeType(file) else "",
) {
    // File type properties
    val isImage: Boolean by lazy {
        if (isDirectory) false
        else isImageFile(file)
    }

    val isVideo: Boolean by lazy {
        if (isDirectory) false
        else isVideoFile(file)
    }

    val isAudio: Boolean by lazy {
        if (isDirectory) false
        else isAudioFile(file)
    }

    val isDocument: Boolean by lazy {
        if (isDirectory) false
        else isDocumentFile(file)
    }

    val isArchive: Boolean by lazy {
        if (isDirectory) false
        else isArchiveFile(file)
    }

    val isApk: Boolean by lazy {
        if (isDirectory) false
        else isApkFile(file)
    }
    val size: Long by lazy {
        if (isDirectory) {
            directorySizeCache[path]?.let { return@lazy it }

            val size = calculateDirectorySizeWithDepth(file, 1)

            directorySizeCache[path] = size
            size
        } else {
            file.length()
        }
    }

    val itemCount: Int by lazy {
        if (isDirectory) {
            directoryItemCountCache[path]?.let { return@lazy it }

            val count = countItems(file)

            // Cache the result
            directoryItemCountCache[path] = count
            count
        } else {
            0
        }
    }
    companion object {
        private val directorySizeCache = ConcurrentHashMap<String, Long>()

        private val directoryItemCountCache = ConcurrentHashMap<String, Int>()

        private const val MAX_CACHE_SIZE = 100

        /**
         * Calculate directory size (no depth limit, not recommended)
         */
        private fun calculateDirectorySize(directory: File): Long {
            var size: Long = 0
            try {
                val files = directory.listFiles()
                if (files != null) {
                    for (file in files) {
                        size += if (file.isDirectory) {
                            calculateDirectorySize(file)
                        } else {
                            file.length()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return size
        }

        /**
         * Calculate directory size (with depth limit)
         * @param directory The directory
         * @param maxDepth Maximum recursion depth
         */
        private fun calculateDirectorySizeWithDepth(directory: File, maxDepth: Int): Long {
            if (maxDepth < 0) {
                return 0
            }

            var size: Long = 0
            try {
                val files = directory.listFiles()
                if (files != null) {
                    for (file in files) {
                        size += if (file.isDirectory) {
                            if (maxDepth > 0) {
                                // 递归计算子目录大小，深度减1
                                calculateDirectorySizeWithDepth(file, maxDepth - 1)
                            } else {
                                // 达到最大深度，不再递归
                                0
                            }
                        } else {
                            file.length()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return size
        }

        /**
         * Count items in directory
         */
        private fun countItems(directory: File): Int {
            val files = directory.listFiles()
            return files?.size ?: 0
        }

        /**
         * Check if file is an image
         */
        private fun isImageFile(file: File): Boolean {
            val imageExtensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "svg")
            return imageExtensions.contains(file.extension.lowercase())
        }

        /**
         * Check if file is a video
         */
        private fun isVideoFile(file: File): Boolean {
            val videoExtensions = listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp")
            return videoExtensions.contains(file.extension.lowercase())
        }

        /**
         * Check if file is an audio
         */
        private fun isAudioFile(file: File): Boolean {
            val audioExtensions = listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a")
            return audioExtensions.contains(file.extension.lowercase())
        }

        /**
         * Check if file is a document
         */
        private fun isDocumentFile(file: File): Boolean {
            val docExtensions = listOf("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rtf")
            return docExtensions.contains(file.extension.lowercase())
        }

        /**
         * Check if file is an archive
         */
        private fun isArchiveFile(file: File): Boolean {
            val archiveExtensions = listOf("zip", "rar", "7z", "tar", "gz", "bz2", "xz")
            return archiveExtensions.contains(file.extension.lowercase())
        }

        /**
         * Check if file is an APK
         */
        private fun isApkFile(file: File): Boolean {
            return file.extension.lowercase() == "apk"
        }
    }
}
