package com.example.clean0522.domain.model

/**
 * Represents a group of files
 */
data class FileGroup(
    val id: String,
    val name: String,
    val files: List<FileItem>,
    val totalSize: Long = files.sumOf { it.size },
    val itemCount: Int = files.size,
    val isExpanded: Boolean = false,
    val isSelected: Boolean = false
) {
    companion object {
        /**
         * Create a file group from a list of files
         */
        fun fromFiles(name: String, files: List<FileItem>): FileGroup {
            return FileGroup(
                id = name.lowercase().replace(" ", "_"),
                name = name,
                files = files
            )
        }
    }
}

/**
 * Represents different types of redundant file categories
 */
enum class RedundantFileType(val displayName: String) {
    APKS("APKs"),
    DOWNLOAD_FILES("Download Files"),
    SCREENSHOTS("Screenshots"),
    LOG_FILES("Log Files"),
    TEMP_FILES("Temp Files")
}

/**
 * Represents file type categories for large files
 */
enum class LargeFileType(val displayName: String) {
    ALL("All"),
    IMAGES("images"),
    AUDIOS("audios"),
    VIDEOS("videos"),
    OTHERS("others")
}
