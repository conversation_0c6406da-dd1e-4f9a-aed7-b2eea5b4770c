package com.example.clean0522

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Checkbox
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.clean0522.ui.features.browser.ApksBrowserContent
import com.example.clean0522.ui.features.browser.AudiosBrowserContent
import com.example.clean0522.ui.features.browser.DocsBrowserContent
import com.example.clean0522.ui.features.browser.ImagesBrowserContent
import com.example.clean0522.ui.features.browser.MediaBrowserViewModel
import com.example.clean0522.ui.features.browser.StorageBrowserContent
import com.example.clean0522.ui.features.browser.StorageBrowserViewModel
import com.example.clean0522.ui.features.browser.VideosBrowserContent
import com.example.clean0522.ui.features.browser.ZipsBrowserContent
import com.example.clean0522.ui.navigation.Screen
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.ui.components.CustomCheckbox
import com.example.clean0522.ui.components.FileOperationBar
import com.example.clean0522.ui.components.RemoveButton
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.utils.FileUtils

/**
 * Activity for file browser screens
 */
class FileBrowserActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_BROWSER_TYPE = "browser_type"
        const val TYPE_STORAGE = "storage"
        const val TYPE_IMAGES = "images"
        const val TYPE_VIDEOS = "videos"
        const val TYPE_AUDIOS = "audios"
        const val TYPE_DOCS = "docs"
        const val TYPE_ZIPS = "zips"
        const val TYPE_APKS = "apks"
    }

    @Composable
    override fun setHomePage() {
        val browserType = intent.getStringExtra(EXTRA_BROWSER_TYPE) ?: TYPE_STORAGE
        Clean0522Theme {
            FileBrowserScreen(
                browserType = browserType,
                onBackClick = {
                    finish()
                }
            )
        }
    }
}

/**
 * File browser screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FileBrowserScreen(
    browserType: String,
    onBackClick: () -> Unit
) {
    val navController = rememberNavController()

    if (browserType == FileBrowserActivity.TYPE_STORAGE){
        val storageBrowserViewMode: StorageBrowserViewModel = viewModel()
        val isSelectionMode = storageBrowserViewMode.isSelectionMode

        BackHandler {
            if (isSelectionMode) {
                storageBrowserViewMode.exitSelectionMode()
            }else{
                if (!storageBrowserViewMode.isRootDirectory()){
                    storageBrowserViewMode.navigateToParentDirectory()
                }else{
                    onBackClick()
                }
            }
        }

        Scaffold(
            modifier = Modifier.navigationBarsPadding(),
            containerColor = colorResource(R.color.bg_color),
            topBar = {
                TopNavBar(
                    title = stringResource(R.string.browser_storage),
                    showBackButton = true,
                    backButtonAction = {
                        if (isSelectionMode) {
                            storageBrowserViewMode.exitSelectionMode()
                        }else{
                            if (!storageBrowserViewMode.isRootDirectory()){
                                storageBrowserViewMode.navigateToParentDirectory()
                            }else{
                                onBackClick()
                            }
                        }
                    },
                    settingsButtonContent = {
                        if (isSelectionMode) {
                            val fileList = storageBrowserViewMode.fileList
                            val selectedFiles = storageBrowserViewMode.selectedFiles
                            val allSelected = fileList.isNotEmpty() && selectedFiles.size == fileList.size

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = stringResource(R.string.tab_all),
                                    modifier = Modifier.padding(end = 4.dp),
                                    colorResource(R.color.text_black),
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.SemiBold
                                )
                                CustomCheckbox(
                                    checked = allSelected,
                                    onCheckedChange = { storageBrowserViewMode.toggleSelectAll() },
                                    size = 18.dp
                                )
                            }

                        } else {
                            // Show sort button in normal mode
                            Image(painter = painterResource(R.mipmap.ic_sort),
                                contentDescription = stringResource(R.string.sort),
                                modifier = Modifier.size(24.dp)
                                    .clickable {
                                        storageBrowserViewMode.showSortDialog()
                                    }
                            )
                        }
                    }
                )
            },
            bottomBar = {
                if (isSelectionMode) {
                    StorageBrowserBottomBar(viewModel = storageBrowserViewMode)
                }
            }
        ) { innerPadding ->
            Box(modifier = Modifier.padding(innerPadding)){
                StorageBrowserContent(storageBrowserViewMode)
            }
        }
    }else{
        val mediaBrowserViewModel: MediaBrowserViewModel = viewModel()
        val mediaSelectionMode = mediaBrowserViewModel.isSelectionMode
        BackHandler {
            if (mediaSelectionMode) {
                mediaBrowserViewModel.exitSelectionMode()
            }else{
                onBackClick()
            }
        }

        Scaffold(
            containerColor = colorResource(R.color.bg_color),
            modifier = Modifier.navigationBarsPadding(),
            topBar = {
                TopNavBar(
                    title = when (browserType) {
                        FileBrowserActivity.TYPE_IMAGES -> stringResource(R.string.browser_images)
                        FileBrowserActivity.TYPE_VIDEOS -> stringResource(R.string.browser_videos)
                        FileBrowserActivity.TYPE_AUDIOS -> stringResource(R.string.browser_audios)
                        FileBrowserActivity.TYPE_DOCS -> stringResource(R.string.browser_docs)
                        FileBrowserActivity.TYPE_ZIPS -> stringResource(R.string.browser_zips)
                        FileBrowserActivity.TYPE_APKS -> stringResource(R.string.browser_apks)
                        else -> stringResource(R.string.browser_file)},

                    showBackButton = true,
                    backButtonAction = {
                        if (mediaSelectionMode) {
                            mediaBrowserViewModel.exitSelectionMode()
                        }else{
                            onBackClick()
                        }
                    },
                    settingsButtonContent = {
                        if (mediaSelectionMode) {
                            val allFiles = getAllFilesForBrowserType(browserType, mediaBrowserViewModel)
                            val selectedFiles = mediaBrowserViewModel.selectedFiles
                            val allSelected = allFiles.isNotEmpty() && selectedFiles.size == allFiles.size


                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = stringResource(R.string.tab_all),
                                    modifier = Modifier.padding(end = 4.dp),
                                    colorResource(R.color.text_black),
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.SemiBold
                                )
                                CustomCheckbox(
                                    checked = allSelected,
                                    onCheckedChange = {
                                        mediaBrowserViewModel.toggleSelectAll(allFiles)
                                    },
                                    size = 18.dp
                                )
                            }
                        } else {
                            if (browserType != FileBrowserActivity.TYPE_IMAGES &&
                                browserType != FileBrowserActivity.TYPE_VIDEOS) {
                                Image(painter = painterResource(R.mipmap.ic_sort),
                                    contentDescription = stringResource(R.string.sort),
                                    modifier = Modifier.size(24.dp)
                                        .clickable {
                                            mediaBrowserViewModel.showSortDialog()
                                        }
                                )
                            }
                        }
                    }
                )
            },
            bottomBar = {
                if (mediaSelectionMode) {
                    MediaBrowserBottomBar(viewModel = mediaBrowserViewModel)
                }
            }
        ) { innerPadding ->
            Box(modifier = Modifier.padding(innerPadding)) {
                FileBrowserNavHost(
                    navController = navController,
                    browserType = browserType
                )
            }
        }
    }
}

/**
 * Navigation host for file browser screens
 */
@Composable
fun FileBrowserNavHost(
    navController: NavHostController,
    mediaBrowserViewModel: MediaBrowserViewModel = viewModel(),
    browserType: String
) {
    val startDestination = when (browserType) {
        FileBrowserActivity.TYPE_STORAGE -> Screen.StorageBrowser.route
        FileBrowserActivity.TYPE_IMAGES -> Screen.ImagesBrowser.route
        FileBrowserActivity.TYPE_VIDEOS -> Screen.VideosBrowser.route
        FileBrowserActivity.TYPE_AUDIOS -> Screen.AudiosBrowser.route
        FileBrowserActivity.TYPE_DOCS -> Screen.DocsBrowser.route
        FileBrowserActivity.TYPE_ZIPS -> Screen.ZipsBrowser.route
        FileBrowserActivity.TYPE_APKS -> Screen.ApksBrowser.route
        else -> Screen.StorageBrowser.route
    }

    Box(modifier = Modifier
        .padding(horizontal = 16.dp, vertical = 8.dp)){
        NavHost(
            navController = navController,
            startDestination = startDestination
        ) {
            composable(Screen.ImagesBrowser.route) {
                ImagesBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.VideosBrowser.route) {
                VideosBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.AudiosBrowser.route) {
                AudiosBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.DocsBrowser.route) {
                DocsBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.ZipsBrowser.route) {
                ZipsBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.ApksBrowser.route) {
                ApksBrowserContent(mediaBrowserViewModel)
            }
        }
    }
}

/**
 * Get all files for the current browser type
 */
private fun getAllFilesForBrowserType(
    browserType: String,
    viewModel: MediaBrowserViewModel
): List<com.example.clean0522.domain.model.MediaFile> {
    return when (browserType) {
        FileBrowserActivity.TYPE_IMAGES -> viewModel.imageGroups.flatMap { it.files }
        FileBrowserActivity.TYPE_VIDEOS -> viewModel.videoGroups.flatMap { it.files }
        FileBrowserActivity.TYPE_AUDIOS -> viewModel.audioFiles
        FileBrowserActivity.TYPE_DOCS -> viewModel.filteredDocumentFiles
        FileBrowserActivity.TYPE_ZIPS -> viewModel.archiveFiles
        FileBrowserActivity.TYPE_APKS -> viewModel.apkFiles
        else -> emptyList()
    }
}

/**
 * Bottom bar for storage browser operations
 */
@Composable
fun StorageBrowserBottomBar(viewModel: StorageBrowserViewModel) {
    val context = LocalContext.current

    if (viewModel.selectedFiles.size == 1) {
        FileOperationBar(
            onDetailClick = { viewModel.showFileDetails() },
            onOpenClick = {
                viewModel.currentSelectedFile?.let { fileItem ->
                    if (!fileItem.isDirectory) {
                        FileUtils.openFile(context, fileItem.file)
                    }
                }
            },
            onShareClick = {
                viewModel.currentSelectedFile?.let { fileItem ->
                    if (!fileItem.isDirectory) {
                        FileUtils.shareFile(context, fileItem.file)
                    }
                }
            },
            onRenameClick = { viewModel.showRenameFile() },
            onRemoveClick = { viewModel.showDeleteConfirmation() }
        )
    } else if (viewModel.selectedFiles.size > 1) {
        RemoveButton(
            selectedSize = viewModel.getSelectedFilesSize(),
            onClick = { viewModel.showDeleteConfirmation() }
        )
    }
}

/**
 * Bottom bar for media browser operations
 */
@Composable
fun MediaBrowserBottomBar(viewModel: MediaBrowserViewModel) {
    val context = LocalContext.current

    if (viewModel.selectedFiles.size == 1) {
        FileOperationBar(
            onDetailClick = { viewModel.showFileDetails() },
            onOpenClick = {
                viewModel.currentSelectedFile?.let { file ->
                    FileUtils.openFile(context, file.file)
                }
            },
            onShareClick = {
                viewModel.currentSelectedFile?.let { file ->
                    FileUtils.shareFile(context, file.file)
                }
            },
            onRenameClick = { viewModel.showRenameFile() },
            onRemoveClick = { viewModel.showDeleteConfirmation() }
        )
    } else if (viewModel.selectedFiles.size > 1) {
        RemoveButton(
            selectedSize = viewModel.getSelectedFilesSize(),
            onClick = { viewModel.showDeleteConfirmation() }
        )
    }
}