package com.example.clean0522.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.domain.model.GroupedMediaFiles
import com.example.clean0522.domain.model.MediaFile
import com.example.clean0522.ui.features.browser.MediaBrowserViewModel

/**
 * Media grid content for images and videos
 */
@Composable
fun MediaGridContent(
    groupedFiles: List<GroupedMediaFiles>,
    isSelectionMode: Boolean,
    selectedFiles: Set<MediaFile>,
    isLoading: Boolean,
    onFileClick: (MediaFile) -> Unit,
    onFileLongClick: (MediaFile) -> Unit,
    onToggleSelection: (MediaFile) -> Unit,
) {
    Box(modifier = Modifier.fillMaxSize()) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (groupedFiles.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(R.string.empty_folder))
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize()
            ) {
                items(groupedFiles) { group ->
                    Column {
                        // Date header
                        Text(
                            text = group.date,
                            color = colorResource(R.color.text_black),
                            fontWeight = FontWeight.SemiBold,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(0.dp, 0.dp, 0.dp, 16.dp)
                        )

                        // Grid of thumbnails
                        val configuration = LocalConfiguration.current
                        val screenWidth = configuration.screenWidthDp.dp
                        val horizontalPadding = 0.dp * 2
                        val horizontalSpacing = 4.dp * 3
                        val itemWidth = (screenWidth - horizontalPadding - horizontalSpacing) / 4
                        val itemHeight = itemWidth

                        LazyVerticalGrid(
                            columns = GridCells.Fixed(4),
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(calculateGridHeight(group.files.size, 4, itemHeight, 6.dp))
                                .padding(),
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalArrangement = Arrangement.spacedBy(6.dp)
                        ) {
                            items(group.files) { file ->
                                MediaThumbnailItem(
                                    mediaFile = file,
                                    isSelectionMode = isSelectionMode,
                                    isSelected = selectedFiles.contains(file),
                                    onClick = { onFileClick(file) },
                                    onLongClick = { onFileLongClick(file) },
                                    onCheckboxClick = { onToggleSelection(file) }
                                )
                            }
                        }
                    }
                }
            }


        }
    }
}

/**
 * Media list content for audio, documents, archives, and APKs
 */
@Composable
fun MediaListContent(
    files: List<MediaFile>,
    isSelectionMode: Boolean,
    selectedFiles: Set<MediaFile>,
    isLoading: Boolean,
    onFileClick: (MediaFile) -> Unit,
    onFileLongClick: (MediaFile) -> Unit,
    onToggleSelection: (MediaFile) -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (files.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(R.string.empty_folder))
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RoundedCornerShape(15.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                LazyColumn{
                    items(files) { file ->
                        MediaListItem(
                            mediaFile = file,
                            isSelectionMode = isSelectionMode,
                            isSelected = selectedFiles.contains(file),
                            onClick = { onFileClick(file) },
                            onLongClick = { onFileLongClick(file) },
                            onCheckboxClick = { onToggleSelection(file) }
                        )

                        if (file != files.last()){
                            Divider(
                                modifier = Modifier.padding(horizontal = 16.dp),
                                thickness = 0.5.dp
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Calculate the exact height needed for a grid layout
 */
private fun calculateGridHeight(
    itemCount: Int,
    columns: Int,
    itemHeight: androidx.compose.ui.unit.Dp,
    verticalSpacing: androidx.compose.ui.unit.Dp
): androidx.compose.ui.unit.Dp {
    if (itemCount == 0) return 0.dp

    // Calculate number of rows needed using ceiling division
    val rows = (itemCount + columns - 1) / columns

    // Calculate total height: (rows * itemHeight) + ((rows - 1) * verticalSpacing)
    val totalItemHeight = itemHeight * rows
    val totalSpacingHeight = if (rows > 1) verticalSpacing * (rows - 1) else 0.dp

    return totalItemHeight + totalSpacingHeight
}

/**
 * Media dialogs component
 */
@Composable
fun MediaDialogs(
    viewModel: MediaBrowserViewModel
) {
    // File details dialog
    if (viewModel.showDetailsDialog && viewModel.currentSelectedFile != null) {
        MediaFileDetailsDialog(
            mediaFile = viewModel.currentSelectedFile!!,
            onDismiss = { viewModel.dismissFileDetails() }
        )
    }

    // Rename dialog
    if (viewModel.showRenameDialog && viewModel.currentSelectedFile != null) {
        RenameFileDialog(
            currentName = viewModel.currentSelectedFile!!.name,
            onDismiss = { viewModel.dismissRenameFile() },
            onConfirm = { newName ->
                // Handle rename logic here
                viewModel.dismissRenameFile()
            }
        )
    }

    // Delete confirmation dialog
    if (viewModel.showDeleteDialog) {
        DeleteConfirmationDialog(
            onDismiss = { viewModel.dismissDeleteConfirmation() },
            onConfirm = {
                // Handle delete logic here
                viewModel.dismissDeleteConfirmation()
            }
        )
    }

    // Sort dialog
    if (viewModel.showSortDialog) {
        SortDialog(
            currentSortOption = viewModel.currentSortOption,
            onSortOptionSelected = { sortOption ->
                viewModel.setSortOption(sortOption)
            },
            onDismiss = { viewModel.dismissSortDialog() }
        )
    }
}
