package com.example.clean0522.ui.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import coil.request.videoFrameMillis
import androidx.compose.ui.platform.LocalContext
import coil.decode.VideoFrameDecoder
import coil.size.Size
import com.example.clean0522.domain.model.MediaFile
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.utils.FileTypeIconUtils
import com.example.clean0522.R

/**
 * Media file list item component for audio, documents, archives, and APKs
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun MediaListItem(
    mediaFile: MediaFile,
    isSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onCheckboxClick: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = onClick,
                onLongClick = onLongClick,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
            .padding(vertical = 16.dp, horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // File icon
        MediaFileIcon(mediaFile = mediaFile)
        
        // File information
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        ) {
            Text(
                text = mediaFile.name,
                style = MaterialTheme.typography.bodyLarge,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Text(
                text = FileUtils.formatFileSize(mediaFile.size),
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                fontSize = 12.sp
            )
        }
        
        // Selection checkbox
        Box(modifier = Modifier.size(18.dp)){
            if (isSelectionMode) {
                CustomCheckbox(
                    checked = isSelected,
                    onCheckedChange = onCheckboxClick,
                    size = 18.dp
                )
            }
        }
    }
}

/**
 * Media file icon component
 */
@Composable
fun MediaFileIcon(mediaFile: MediaFile) {
    val iconRes = FileTypeIconUtils.getFileTypeIconByExtension(mediaFile.extension)

    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(MaterialTheme.shapes.small),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(iconRes),
            contentDescription = mediaFile.name,
            modifier = Modifier.size(40.dp)
        )
    }
}

/**
 * Media thumbnail item component for images and videos
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun MediaThumbnailItem(
    mediaFile: MediaFile,
    isSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onCheckboxClick: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .combinedClickable(
                onClick = onClick,
                onLongClick = onLongClick,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        val context = LocalContext.current

        // Thumbnail
        AsyncImage(
            model = if (mediaFile.isVideo) {
                ImageRequest.Builder(context)
                    .data(mediaFile.file)
                    .size(Size.ORIGINAL)
                    .decoderFactory { result, options, _ -> VideoFrameDecoder(result.source, options) }
                    .crossfade(true)
                    .build()
            } else {
                mediaFile.path
            },
            contentDescription = mediaFile.name,
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(8.dp))
                .background(Color(0xFFEEEEEE)),
            contentScale = ContentScale.Crop,
            placeholder = painterResource(if (mediaFile.isVideo) R.mipmap.video_empty else R.mipmap.empty_image),
            error = painterResource(if (mediaFile.isVideo) R.mipmap.video_empty else R.mipmap.empty_image)
        )

        // Video play icon overlay
        if (mediaFile.isVideo) {
            Box(
                modifier = Modifier
                    .fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_play),
                    contentDescription = "Play video",
                    modifier = Modifier.size(36.dp)
                )
            }
        }
        
        // Selection overlay
        if (isSelectionMode) {
            CustomCheckbox(
                checked = isSelected,
                onCheckedChange = onCheckboxClick,
                uncheckedIcon = R.mipmap.ck_en,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(4.dp),
                size = 18.dp
            )
        }
    }
}

/**
 * Format duration in milliseconds to MM:SS format
 */
private fun formatDuration(durationMs: Long): String {
    val seconds = (durationMs / 1000) % 60
    val minutes = (durationMs / (1000 * 60)) % 60
    val hours = (durationMs / (1000 * 60 * 60))
    
    return if (hours > 0) {
        String.format("%d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%d:%02d", minutes, seconds)
    }
}
