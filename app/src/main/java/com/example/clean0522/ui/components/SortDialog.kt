package com.example.clean0522.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.clean0522.R
import com.example.clean0522.domain.model.SortOption
import com.example.clean0522.domain.model.SortOptionItem

/**
 * Sort dialog component
 */
@Composable
fun SortDialog(
    currentSortOption: SortOption,
    onSortOptionSelected: (SortOption) -> Unit,
    onDismiss: () -> Unit
) {
    val sortOptions = listOf(
        SortOptionItem(SortOption.NAME_A_TO_Z, R.string.sort_name_a_to_z),
        SortOptionItem(SortOption.NAME_Z_TO_A, R.string.sort_name_z_to_a),
        SortOptionItem(SortOption.NEWEST_FIRST, R.string.sort_newest_first),
        SortOptionItem(SortOption.OLDEST_FIRST, R.string.sort_oldest_first),
        SortOptionItem(SortOption.SMALL_TO_LARGE, R.string.sort_small_to_large),
        SortOptionItem(SortOption.LARGE_TO_SMALL, R.string.sort_large_to_small)
    )
    
    Dialog(
        onDismissRequest = {},
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(modifier = Modifier.fillMaxWidth()){
                    Image(painter = painterResource(R.mipmap.icon_close),
                        contentDescription = "close",
                        modifier = Modifier
                            .size(24.dp)
                            .align(Alignment.CenterEnd)
                            .clickable { onDismiss() })
                }

                Text(
                    text = stringResource(R.string.sort_by),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Sort options
                LazyColumn {
                    items(sortOptions) { sortOptionItem ->
                        SortOptionRow(
                            sortOptionItem = sortOptionItem,
                            isSelected = sortOptionItem.option == currentSortOption,
                            onClick = {
                                onSortOptionSelected(sortOptionItem.option)
                                onDismiss()
                            }
                        )

                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onDismiss,
                    modifier = Modifier
                        .widthIn(min = 150.dp)
                        .height(48.dp),
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(R.color.text_blue)
                    )
                ) {
                    Text(
                        text = stringResource(R.string.confirm),
                        color = Color.White,
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp
                    )
                }
            }
        }
    }
}

/**
 * Sort option row component
 */
@Composable
fun SortOptionRow(
    sortOptionItem: SortOptionItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(color = if (isSelected) Color(0xFFE6EDFF) else Color(0xFFF6F6F6), shape = RoundedCornerShape(4.dp))
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {

        Text(
            text = stringResource(sortOptionItem.displayNameResId),
            color = if (isSelected) colorResource(R.color.text_blue) else colorResource(R.color.text_gray_70)
        )

        Spacer(modifier = Modifier.weight(1f))

        if (isSelected){
            Image(painter = painterResource(R.mipmap.icon_select),
                contentDescription = "check",
                modifier = Modifier
                    .size(24.dp))
        }
    }
}
