package com.example.clean0522.ui.features.browser

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.R
import com.example.clean0522.ui.components.*
import com.example.clean0522.utils.FileUtils

/**
 * Storage browser content composable
 */
@Composable
fun StorageBrowserContent(
    viewModel: StorageBrowserViewModel = viewModel()
) {
    val fileList = viewModel.fileList
    val currentPath = viewModel.currentPath
    val isSelectionMode = viewModel.isSelectionMode
    val selectedFiles = viewModel.selectedFiles
    val currentSelectedFile = viewModel.currentSelectedFile
    val context = LocalContext.current

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            DirectoryPathBar(
                currentPath = currentPath,
                onPathSegmentClick = { path ->
                    viewModel.navigateToDirectory(path)
                }
            )

            if (fileList.isEmpty()) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Image(painter = painterResource(R.mipmap.empty_file),
                        contentDescription = null,
                        modifier = Modifier.size(150.dp))

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(text = stringResource(R.string.empty_folder),
                        fontSize = 14.sp,
                        color = colorResource(R.color.text_gray_70),
                        fontWeight = FontWeight.Medium
                    )
                }
            } else {
                Box(modifier = Modifier.weight(1f)
                    .padding(bottom = 8.dp, start = 16.dp, end = 16.dp)){
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                        shape = RoundedCornerShape(15.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        )
                    ) {
                        LazyColumn{
                            items(fileList) { fileItem ->
                                FileListItem(
                                    isFolder = true,
                                    fileItem = fileItem,
                                    isSelectionMode = isSelectionMode,
                                    isSelected = selectedFiles.contains(fileItem),
                                    onClick = {
                                        viewModel.openFileOrDirectory(context,fileItem)
                                    },
                                    onLongClick = {
                                        viewModel.onFileLongClick(fileItem)
                                    },
                                    onCheckboxClick = { isChecked ->
                                        viewModel.toggleFileSelection(fileItem)
                                    },
                                    iconClick = {
                                        FileUtils.openFile(context,fileItem.file)
                                    }
                                )

                                HorizontalDivider(
                                    modifier = Modifier.padding(horizontal = 16.dp),
                                    thickness = 0.5.dp
                                )
                            }
                        }
                    }
                }
            }


        }

        if (viewModel.showDetailsDialog && currentSelectedFile != null) {
            FileDetailsDialog(
                fileItem = currentSelectedFile,
                onDismiss = { viewModel.dismissFileDetails() }
            )
        }

        if (viewModel.showRenameDialog && currentSelectedFile != null) {
            RenameFileDialog(
                currentName = currentSelectedFile.name,
                onDismiss = { viewModel.dismissRenameFile() },
                onConfirm = { newName -> viewModel.renameFile(newName) }
            )
        }

        if (viewModel.showDeleteDialog) {
            DeleteConfirmationDialog(
                onDismiss = { viewModel.dismissDeleteConfirmation() },
                onConfirm = { viewModel.deleteSelectedFiles() }
            )
        }

        // Sort dialog
        if (viewModel.showSortDialog) {
            SortDialog(
                currentSortOption = viewModel.currentSortOption,
                onSortOptionSelected = { sortOption ->
                    viewModel.setSortOption(sortOption)
                },
                onDismiss = { viewModel.dismissSortDialog() }
            )
        }
    }
}

/**
 * Images browser content composable
 */
@Composable
fun ImagesBrowserContent(
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadImages()
    }

    MediaGridContent(
        groupedFiles = viewModel.imageGroups,
        isSelectionMode = viewModel.isSelectionMode,
        selectedFiles = viewModel.selectedFiles,
        isLoading = viewModel.isLoading,
        onFileClick = { it ->
            viewModel.onFileClick(context,it)
        },
        onFileLongClick = viewModel::onFileLongClick,
        onToggleSelection = viewModel::toggleFileSelection
    )

    // Dialogs
    MediaDialogs(
        viewModel = viewModel
    )
}

/**
 * Videos browser content composable
 */
@Composable
fun VideosBrowserContent(
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadVideos()
    }

    MediaGridContent(
        groupedFiles = viewModel.videoGroups,
        isSelectionMode = viewModel.isSelectionMode,
        selectedFiles = viewModel.selectedFiles,
        isLoading = viewModel.isLoading,
        onFileClick = { it -> viewModel.onFileClick(context,it) },
        onFileLongClick = viewModel::onFileLongClick,
        onToggleSelection = viewModel::toggleFileSelection
    )

    // Dialogs
    MediaDialogs(
        viewModel = viewModel
    )
}

/**
 * Audios browser content composable
 */
@Composable
fun AudiosBrowserContent(
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadAudios()
    }

    MediaListContent(
        files = viewModel.audioFiles,
        isSelectionMode = viewModel.isSelectionMode,
        selectedFiles = viewModel.selectedFiles,
        isLoading = viewModel.isLoading,
        onFileClick = { it -> viewModel.onFileClick(context,it) },
        onFileLongClick = viewModel::onFileLongClick,
        onToggleSelection = viewModel::toggleFileSelection
    )

    // Dialogs
    MediaDialogs(
        viewModel = viewModel
    )
}

/**
 * Documents browser content composable
 */
@Composable
fun DocsBrowserContent(
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadDocuments()
    }

    Column(modifier = Modifier.fillMaxSize()) {
        // Document type filter
        DocumentTypeFilter(
            selectedType = viewModel.selectedDocumentType,
            onTypeSelected = viewModel::setDocumentType
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Document list
        MediaListContent(
            files = viewModel.filteredDocumentFiles,
            isSelectionMode = viewModel.isSelectionMode,
            selectedFiles = viewModel.selectedFiles,
            isLoading = viewModel.isLoading,
            onFileClick = { it -> viewModel.onFileClick(context,it) },
            onFileLongClick = viewModel::onFileLongClick,
            onToggleSelection = viewModel::toggleFileSelection
        )
    }

    // Dialogs
    MediaDialogs(
        viewModel = viewModel
    )
}

/**
 * Archives browser content composable
 */
@Composable
fun ZipsBrowserContent(
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadArchives()
    }

    MediaListContent(
        files = viewModel.archiveFiles,
        isSelectionMode = viewModel.isSelectionMode,
        selectedFiles = viewModel.selectedFiles,
        isLoading = viewModel.isLoading,
        onFileClick = { it -> viewModel.onFileClick(context,it) },
        onFileLongClick = viewModel::onFileLongClick,
        onToggleSelection = viewModel::toggleFileSelection
    )

    // Dialogs
    MediaDialogs(
        viewModel = viewModel
    )
}

/**
 * APKs browser content composable
 */
@Composable
fun ApksBrowserContent(
    viewModel: MediaBrowserViewModel = viewModel()
) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadApks()
    }

    MediaListContent(
        files = viewModel.apkFiles,
        isSelectionMode = viewModel.isSelectionMode,
        selectedFiles = viewModel.selectedFiles,
        isLoading = viewModel.isLoading,
        onFileClick = { it -> viewModel.onFileClick(context,it) },
        onFileLongClick = viewModel::onFileLongClick,
        onToggleSelection = viewModel::toggleFileSelection
    )

    // Dialogs
    MediaDialogs(
        viewModel = viewModel
    )
}
