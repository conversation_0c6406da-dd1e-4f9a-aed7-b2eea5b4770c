package com.example.clean0522.ui.features.browser

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.CleanApp
import com.example.clean0522.data.provider.MediaProvider
import com.example.clean0522.domain.model.DocumentType
import com.example.clean0522.domain.model.GroupedMediaFiles
import com.example.clean0522.domain.model.MediaFile
import com.example.clean0522.domain.model.SortOption
import com.example.clean0522.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

/**
 * ViewModel for media browser screens
 */
class MediaBrowserViewModel() : ViewModel() {

    private val mediaProvider = MediaProvider(CleanApp.appContext)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    // Images data
    var imageGroups by mutableStateOf<List<GroupedMediaFiles>>(emptyList())
        private set

    // Videos data
    var videoGroups by mutableStateOf<List<GroupedMediaFiles>>(emptyList())
        private set

    // Audios data
    var audioFiles by mutableStateOf<List<MediaFile>>(emptyList())
        private set

    // Documents data
    var documentFiles by mutableStateOf<List<MediaFile>>(emptyList())
        private set

    var filteredDocumentFiles by mutableStateOf<List<MediaFile>>(emptyList())
        private set

    var selectedDocumentType by mutableStateOf(DocumentType.ALL)
        private set

    // Archives data
    var archiveFiles by mutableStateOf<List<MediaFile>>(emptyList())
        private set

    // APKs data
    var apkFiles by mutableStateOf<List<MediaFile>>(emptyList())
        private set

    // Selection state
    var isSelectionMode by mutableStateOf(false)
        private set

    var selectedFiles by mutableStateOf<Set<MediaFile>>(emptySet())
        private set

    var currentSelectedFile by mutableStateOf<MediaFile?>(null)
        private set

    // Dialog states
    var showDetailsDialog by mutableStateOf(false)
        private set

    var showRenameDialog by mutableStateOf(false)
        private set

    var showDeleteDialog by mutableStateOf(false)
        private set

    // Loading state
    var isLoading by mutableStateOf(false)
        private set

    // Sort state
    var currentSortOption by mutableStateOf(SortOption.NEWEST_FIRST)
        private set

    var showSortDialog by mutableStateOf(false)
        private set

    /**
     * Load images and group by date
     */
    fun loadImages() {
        viewModelScope.launch {
            isLoading = true
            try {
                val images = withContext(Dispatchers.IO) {
                    mediaProvider.getImages()
                }
                imageGroups = groupFilesByDate(images)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load videos and group by date
     */
    fun loadVideos() {
        viewModelScope.launch {
            isLoading = true
            try {
                val videos = withContext(Dispatchers.IO) {
                    mediaProvider.getVideos()
                }
                videoGroups = groupFilesByDate(videos)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load audio files
     */
    fun loadAudios() {
        viewModelScope.launch {
            isLoading = true
            try {
                audioFiles = withContext(Dispatchers.IO) {
                    mediaProvider.getAudios()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load document files
     */
    fun loadDocuments() {
        viewModelScope.launch {
            isLoading = true
            try {
                val documents = withContext(Dispatchers.IO) {
                    mediaProvider.getDocuments()
                }
                documentFiles = documents
                filterDocuments()
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load archive files
     */
    fun loadArchives() {
        viewModelScope.launch {
            isLoading = true
            try {
                archiveFiles = withContext(Dispatchers.IO) {
                    mediaProvider.getArchives()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load APK files
     */
    fun loadApks() {
        viewModelScope.launch {
            isLoading = true
            try {
                apkFiles = withContext(Dispatchers.IO) {
                    mediaProvider.getApks()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Set document type filter
     */
    fun setDocumentType(type: DocumentType) {
        selectedDocumentType = type
        filterDocuments()
    }

    /**
     * Filter documents by type
     */
    private fun filterDocuments() {
        filteredDocumentFiles = when (selectedDocumentType) {
            DocumentType.ALL -> documentFiles
            else -> documentFiles.filter { it.documentType == selectedDocumentType }
        }
    }

    /**
     * Group files by date
     */
    private fun groupFilesByDate(files: List<MediaFile>): List<GroupedMediaFiles> {
        return files.groupBy { file ->
            val date = if (file.dateTaken > 0) file.dateTaken else file.dateAdded
            dateFormat.format(Date(date))
        }.map { (date, files) ->
            GroupedMediaFiles(date, files)
        }.sortedByDescending { it.date }
    }

    /**
     * Handle file click
     */
    fun onFileClick(context: Context, file: MediaFile) {
        if (isSelectionMode) {
            toggleFileSelection(file)
        } else {
            currentSelectedFile = file
            FileUtils.openFile(context, file.file)
        }
    }

    /**
     * Handle file long click
     */
    fun onFileLongClick(file: MediaFile) {
        isSelectionMode = true
        currentSelectedFile = file
        selectedFiles = selectedFiles + file
    }

    /**
     * Toggle file selection
     */
    fun toggleFileSelection(file: MediaFile) {
        selectedFiles = if (selectedFiles.contains(file)) {
            selectedFiles - file
        } else {
            selectedFiles + file
        }

//        if (selectedFiles.isEmpty()) {
//            exitSelectionMode()
//        }
    }

    /**
     * Exit selection mode
     */
    fun exitSelectionMode() {
        isSelectionMode = false
        selectedFiles = emptySet()
        currentSelectedFile = null
    }

    /**
     * Toggle select all
     */
    fun toggleSelectAll(allFiles: List<MediaFile>) {
        selectedFiles = if (selectedFiles.size == allFiles.size) {
            emptySet()
        } else {
            allFiles.toSet()
        }

        if (selectedFiles.isEmpty()) {
            exitSelectionMode()
        }
    }

    /**
     * Get selected files size
     */
    fun getSelectedFilesSize(): String {
        val totalSize = selectedFiles.sumOf { it.size }
        return FileUtils.formatFileSize(totalSize)
    }

    /**
     * Show file details dialog
     */
    fun showFileDetails() {
        showDetailsDialog = true
    }

    /**
     * Dismiss file details dialog
     */
    fun dismissFileDetails() {
        showDetailsDialog = false
    }

    /**
     * Show rename dialog
     */
    fun showRenameFile() {
        showRenameDialog = true
    }

    /**
     * Dismiss rename dialog
     */
    fun dismissRenameFile() {
        showRenameDialog = false
    }

    /**
     * Show delete confirmation dialog
     */
    fun showDeleteConfirmation() {
        showDeleteDialog = true
    }

    /**
     * Dismiss delete confirmation dialog
     */
    fun dismissDeleteConfirmation() {
        showDeleteDialog = false
    }

    /**
     * Show sort dialog
     */
    fun showSortDialog() {
        showSortDialog = true
    }

    /**
     * Dismiss sort dialog
     */
    fun dismissSortDialog() {
        showSortDialog = false
    }

    /**
     * Set sort option and re-sort files
     */
    fun setSortOption(sortOption: SortOption) {
        currentSortOption = sortOption
        applySorting()
    }

    /**
     * Apply current sorting to all file lists
     */
    private fun applySorting() {
        // Sort images
        imageGroups = imageGroups.map { group ->
            group.copy(files = sortFiles(group.files))
        }

        // Sort videos
        videoGroups = videoGroups.map { group ->
            group.copy(files = sortFiles(group.files))
        }

        // Sort other file types
        audioFiles = sortFiles(audioFiles)
        documentFiles = sortFiles(documentFiles)
        archiveFiles = sortFiles(archiveFiles)
        apkFiles = sortFiles(apkFiles)

        // Re-filter documents if needed
        filterDocuments()
    }

    /**
     * Sort files based on current sort option
     */
    private fun sortFiles(files: List<MediaFile>): List<MediaFile> {
        return when (currentSortOption) {
            SortOption.NAME_A_TO_Z -> files.sortedBy { it.name.lowercase() }
            SortOption.NAME_Z_TO_A -> files.sortedByDescending { it.name.lowercase() }
            SortOption.NEWEST_FIRST -> files.sortedByDescending {
                if (it.dateTaken > 0) it.dateTaken else it.dateAdded
            }
            SortOption.OLDEST_FIRST -> files.sortedBy {
                if (it.dateTaken > 0) it.dateTaken else it.dateAdded
            }
            SortOption.SMALL_TO_LARGE -> files.sortedBy { it.size }
            SortOption.LARGE_TO_SMALL -> files.sortedByDescending { it.size }
        }
    }
}
