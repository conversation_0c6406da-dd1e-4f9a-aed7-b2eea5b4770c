package com.example.clean0522.ui.navigation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R

@Composable
fun TopNavBar(
    title: String,
    showBackButton: Boolean,
    backButtonAction: () -> Unit = {},
    settingsButtonContent: @Composable () -> Unit = {}
) {
    Row(modifier = Modifier
        .statusBarsPadding()
        .fillMaxWidth()
        .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically) {
        if (showBackButton) {
            Image(painter = painterResource(R.mipmap.icon_return),
                contentDescription = stringResource(R.string.back),
                modifier = Modifier.size(24.dp)
                    .clickable {
                        backButtonAction()
                    }
            )
        }
        Text(text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Start,
            modifier = Modifier.weight(1f)
                .padding(horizontal = 6.dp),
            color = colorResource(R.color.text_black)
        )
        Box(modifier = Modifier){
            settingsButtonContent()
        }
        Spacer(modifier = Modifier.width(6.dp))
    }
}