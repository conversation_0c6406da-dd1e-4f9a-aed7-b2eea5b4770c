package com.example.clean0522.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.clean0522.R
import android.os.Environment
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.example.clean0522.CleanApp

/**
 * Directory path navigation bar component
 */
@Composable
fun DirectoryPathBar(
    currentPath: String,
    onPathSegmentClick: (String) -> Unit
) {
    val scrollState = rememberScrollState()
    val (rootSegment, remainingSegments) = processPath(currentPath)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .horizontalScroll(scrollState)
            .padding( vertical = 8.dp, horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "$rootSegment/",
            fontSize = 14.sp,
            color = colorResource(R.color.text_black),
            fontWeight = FontWeight.Medium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.clickable {
                onPathSegmentClick(getRootPath(currentPath))
            }
        )

        remainingSegments.forEachIndexed { index, segment ->
            Text(
                text = if (index < remainingSegments.lastIndex) "$segment/" else segment,
                fontSize = 14.sp,
                color = colorResource(R.color.text_black),
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.clickable {
                    val pathToSegment = buildPathToSegment(rootSegment, remainingSegments, index)
                    onPathSegmentClick(pathToSegment)
                }
            )
        }
    }
}

/**
 * Process path to treat special root directories as a single unit
 * Returns a Pair where the first element is the root segment and the second is the list of remaining path segments
 */
private fun processPath(path: String): Pair<String, List<String>> {
    val externalStoragePath = Environment.getExternalStorageDirectory().absolutePath

    if (path.startsWith(externalStoragePath)) {
        val rootSegment = CleanApp.appContext.getString(R.string.storage)

        val remainingPath = path.removePrefix(externalStoragePath).trim('/')
        val remainingSegments = if (remainingPath.isEmpty()) {
            emptyList()
        } else {
            remainingPath.split("/")
        }

        return Pair(rootSegment, remainingSegments)
    }

    val specialRoots = listOf(
        "/storage/emulated/0" to CleanApp.appContext.getString(R.string.storage),
        "/storage" to "storage"
    )

    for ((rootPath, rootName) in specialRoots) {
        if (path.startsWith(rootPath)) {
            val remainingPath = path.removePrefix(rootPath).trim('/')
            val remainingSegments = if (remainingPath.isEmpty()) {
                emptyList()
            } else {
                remainingPath.split("/")
            }

            return Pair(rootName, remainingSegments)
        }
    }

    // Default processing
    val segments = path.trim('/').split("/")
    return if (segments.isEmpty()) {
        Pair("/", emptyList())
    } else {
        Pair(segments[0], segments.drop(1))
    }
}

/**
 * Get the root path
 */
private fun getRootPath(path: String): String {
    val externalStoragePath = Environment.getExternalStorageDirectory().absolutePath

    if (path.startsWith(externalStoragePath)) {
        return externalStoragePath
    }

    val specialRoots = listOf(
        "/storage/emulated/0",
        "/storage"
    )

    for (rootPath in specialRoots) {
        if (path.startsWith(rootPath)) {
            return rootPath
        }
    }

    return "/"
}

/**
 * Build path to the specified segment
 */
private fun buildPathToSegment(rootSegment: String, segments: List<String>, segmentIndex: Int): String {
    val rootPath = when (rootSegment) {
        "storage" -> Environment.getExternalStorageDirectory().absolutePath
        else -> "/$rootSegment"
    }

    if (segmentIndex < 0) {
        return rootPath
    }

    val pathSegments = segments.subList(0, segmentIndex + 1)
    return if (pathSegments.isEmpty()) {
        rootPath
    } else {
        "$rootPath/${pathSegments.joinToString("/")}"
    }
}
