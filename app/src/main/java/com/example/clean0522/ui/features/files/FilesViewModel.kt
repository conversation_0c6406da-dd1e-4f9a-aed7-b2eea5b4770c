package com.example.clean0522.ui.features.files

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.domain.model.StorageInfo
import com.example.clean0522.domain.usecase.GetStorageInfoUseCase
import com.example.clean0522.utils.FileUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

/**
 * ViewModel for the Files tab
 */
class FilesViewModel(
    private val getStorageInfoUseCase: GetStorageInfoUseCase
) : ViewModel() {
    
    // UI state for the Files tab
    private val _state = MutableStateFlow(FilesState())
    val state: StateFlow<FilesState> = _state.asStateFlow()
    
    init {
        // Load storage information when ViewModel is created
        loadStorageInfo()
    }
    
    /**
     * Load storage information
     */
    private fun loadStorageInfo() {
        getStorageInfoUseCase()
            .onEach { storageInfo ->
                _state.update { currentState ->
                    currentState.copy(
                        storageInfo = storageInfo,
                        formattedUsedSpace = FileUtils.formatFileSize(storageInfo.usedSpace),
                        formattedTotalSpace = FileUtils.formatFileSize(storageInfo.totalSpace),
                        isLoading = false
                    )
                }
            }
            .catch { error ->
                _state.update { currentState ->
                    currentState.copy(
                        error = error.message ?: "Unknown error occurred",
                        isLoading = false
                    )
                }
            }
            .launchIn(viewModelScope)
    }
    
    /**
     * Refresh storage information
     */
    fun refreshStorageInfo() {
        _state.update { it.copy(isLoading = true) }
        loadStorageInfo()
    }
}

/**
 * UI state for the Files tab
 */
data class FilesState(
    val storageInfo: StorageInfo = StorageInfo.EMPTY,
    val formattedUsedSpace: String = "",
    val formattedTotalSpace: String = "",
    val isLoading: Boolean = true,
    val error: String? = null
)
