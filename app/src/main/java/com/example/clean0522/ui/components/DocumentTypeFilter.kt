package com.example.clean0522.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.domain.model.DocumentType

/**
 * Document type filter component
 */
@Composable
fun DocumentTypeFilter(
    selectedType: DocumentType,
    onTypeSelected: (DocumentType) -> Unit,
    modifier: Modifier = Modifier
) {
    val documentTypes = listOf(
        DocumentType.ALL,
        DocumentType.PDF,
        DocumentType.WORD,
        DocumentType.PPT,
        DocumentType.EXCEL,
        DocumentType.TXT
    )

    Column {
        Row(
            modifier = modifier
                .fillMaxWidth(),
        ) {
            documentTypes.forEach { type ->
                DocumentTypeChip(
                    type = type,
                    isSelected = type == selectedType,
                    onClick = { onTypeSelected(type) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
        Spacer(modifier = Modifier.height(1.dp)
            .fillMaxWidth()
            .background(colorResource(R.color.text_gray)))
    }
}

/**
 * Document type chip component
 */
@Composable
fun DocumentTypeChip(
    type: DocumentType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                if (isSelected) {
                    colorResource(R.color.text_blue)
                } else {
                    Color.Transparent
                }
                , shape = RoundedCornerShape(topStart = 7.dp, topEnd = 7.dp)
            )
            .clickable { onClick() }
            .padding(vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = getDocumentTypeText(type),
            fontWeight = FontWeight.SemiBold,
            color = if (isSelected) {
                Color.White
            } else {
                Color(0xFFA3A3A3)
            },
            fontSize = 12.sp
        )
    }
}

/**
 * Get document type display text
 */
@Composable
private fun getDocumentTypeText(type: DocumentType): String {
    return when (type) {
        DocumentType.ALL -> stringResource(R.string.document_type_all)
        DocumentType.PDF -> stringResource(R.string.document_type_pdf)
        DocumentType.WORD -> stringResource(R.string.document_type_word)
        DocumentType.PPT -> stringResource(R.string.document_type_ppt)
        DocumentType.EXCEL -> stringResource(R.string.document_type_excel)
        DocumentType.TXT -> stringResource(R.string.document_type_txt)
        DocumentType.OTHER -> stringResource(R.string.document_type_other)
    }
}
