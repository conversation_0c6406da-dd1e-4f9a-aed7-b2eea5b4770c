package com.example.clean0522.ui.components

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.material3.LocalTextStyle
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.clean0522.R

/**
 * Privacy dialog for antivirus feature
 */
@Composable
fun AntivirusPrivacyDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.7f)
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.antivirus_about_title),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 16.dp),
                    color = colorResource(R.color.text_black)
                )

                BoxWithConstraints(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {
                    val scrollState = rememberScrollState()
                    val containerHeight = this.maxHeight
                    val context = LocalContext.current

                    val baseText = stringResource(R.string.antivirus_about_content)
                    val linkText = stringResource(R.string.trustlook_privacy_policy_link)
                    val linkUrl = "https://www.trustlook.com/privacy-policy/"

                    val fullText = baseText.format(linkText)

                    val annotatedString = buildAnnotatedString {
                        val startIndex = fullText.indexOf(linkText)
                        if (startIndex != -1) {
                            append(fullText.substring(0, startIndex))

                            pushStringAnnotation(
                                tag = "URL",
                                annotation = linkUrl
                            )
                            withStyle(
                                style = SpanStyle(
                                    color = colorResource(R.color.text_blue_select),
                                    textDecoration = TextDecoration.None
                                )
                            ) {
                                append(linkText)
                            }

                            val endIndex = startIndex + linkText.length
                            if (endIndex < fullText.length) {
                                append(fullText.substring(endIndex))
                            }
                        } else {
                            append(fullText)
                        }
                    }

                    ClickableText(
                        text = annotatedString,
                        style = LocalTextStyle.current.copy(
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            color = colorResource(R.color.text_gray_70)
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .verticalScroll(scrollState)
                            .padding(end = 2.dp),
                        onClick = { offset ->
                            annotatedString.getStringAnnotations(
                                tag = "URL",
                                start = offset,
                                end = offset
                            ).firstOrNull()?.let { annotation ->
                                try {
                                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(annotation.item))
                                    context.startActivity(intent)
                                } catch (e: Exception) {
                                    // Handle error if no browser app is available
                                }
                            }
                        }
                    )

                    if (scrollState.maxValue > 0) {
                        val scrollbarWidth = 4.dp

                        val viewportSize = scrollState.viewportSize
                        val totalContentSize = scrollState.maxValue + viewportSize

                        val visibleRatio = if (totalContentSize > 0) {
                            viewportSize.toFloat() / totalContentSize.toFloat()
                        } else {
                            1f
                        }

                        val scrollProgress = if (scrollState.maxValue > 0) {
                            scrollState.value.toFloat() / scrollState.maxValue.toFloat()
                        } else {
                            0f
                        }

                        Box(
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .width(scrollbarWidth)
                                .height(containerHeight)
                                .padding(vertical = 2.dp)
                        ) {
                            val availableHeight = containerHeight - 4.dp // subtract padding
                            val thumbHeight = maxOf(12.dp, availableHeight * visibleRatio)
                            val maxThumbOffset = availableHeight - thumbHeight

                            Card(
                                modifier = Modifier
                                    .width(scrollbarWidth)
                                    .height(thumbHeight)
                                    .offset(y = maxThumbOffset * scrollProgress),
                                colors = CardDefaults.cardColors(
                                    containerColor = colorResource(R.color.text_blue)
                                ),
                                shape = RoundedCornerShape(2.dp),
                                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                            ) {}
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.text_gray)
                        )
                    ) {
                        Text(
                            text = stringResource(R.string.cancel),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.text_black)
                        )
                    }

                    Button(
                        onClick = onConfirm,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.text_blue)
                        )
                    ) {
                        Text(
                            text = stringResource(R.string.confirm),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}
