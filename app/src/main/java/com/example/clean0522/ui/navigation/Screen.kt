package com.example.clean0522.ui.navigation

/**
 * Sealed class representing all screens in the app
 */
sealed class Screen(val route: String) {
    // Main tabs
    object Files : Screen("files")
    object Antivirus : Screen("antivirus")
    object More : Screen("more")
    
    // Settings
    object Settings : Screen("settings")
    
    // File browser screens
    object StorageBrowser : Screen("storage_browser")
    object ImagesBrowser : Screen("images_browser")
    object VideosBrowser : Screen("videos_browser")
    object AudiosBrowser : Screen("audios_browser")
    object DocsBrowser : Screen("docs_browser")
    object ZipsBrowser : Screen("zips_browser")
    object ApksBrowser : Screen("apks_browser")
    
    // File utility screens
    object LargeFiles : Screen("large_files")
    object RecentFiles : Screen("recent_files")
    object DuplicateFiles : Screen("duplicate_files")
    object RedundantFiles : Screen("redundant_files")
    object SimilarPhotos : Screen("similar_photos")
}
