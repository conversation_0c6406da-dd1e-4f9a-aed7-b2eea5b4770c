package com.example.clean0522.ui.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.decode.VideoFrameDecoder
import coil.request.ImageRequest
import com.example.clean0522.R
import com.example.clean0522.domain.model.FileItem
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.utils.FileTypeIconUtils
import java.text.SimpleDateFormat
import java.util.*

/**
 * File list item component
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FileListItem(
    isFolder: Boolean = false,
    fileItem: FileItem,
    isSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onCheckboxClick: (Boolean) -> Unit,
    iconClick: () -> Unit = {}
) {
    if (!isFolder) {
        FolderStyleListItem(
            fileItem = fileItem,
            isSelectionMode = isSelectionMode,
            isSelected = isSelected,
            onClick = onClick,
            onLongClick = onLongClick,
            onCheckboxClick = onCheckboxClick,
            iconClick = iconClick
        )
    } else {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick,
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                )
                .padding(vertical = 16.dp, horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            FileIcon(fileItem = fileItem,iconClick)

            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 8.dp)
            ) {
                Text(
                    text = fileItem.name,
                    color = colorResource(R.color.text_black),
                    fontWeight = FontWeight.Medium,
                    fontSize = 13.sp,
                    lineHeight = 16.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = if (fileItem.isDirectory) {
                        stringResource(
                            R.string.file_item_count_size,
                            fileItem.itemCount,
                            FileUtils.formatFileSize(fileItem.size)
                        )
                    } else {
                        FileUtils.formatFileSize(fileItem.size)
                    },
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFA3A3A3),
                    fontSize = 12.sp
                )
            }

            if (isSelectionMode) {
                CustomCheckbox(
                    checked = isSelected,
                    onCheckedChange = onCheckboxClick,
                    modifier = Modifier.padding(start = 8.dp),
                    size = 18.dp
                )
            }else if (fileItem.isDirectory){
                Image(painter = painterResource(R.mipmap.ic_arrow),
                    contentDescription = "Arrow",
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

/**
 * File icon component
 */
@Composable
fun FileIcon(fileItem: FileItem,
             clickCallBack: () -> Unit = {}
) {
    val context = LocalContext.current

    Box(
        modifier = Modifier
            .size(50.dp)
            .clip(RoundedCornerShape(6.dp)),
        contentAlignment = Alignment.Center
    ) {
        when {
            fileItem.isImage -> {
                // Display image thumbnail
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(fileItem.file)
                        .crossfade(true)
                        .build(),
                    contentDescription = fileItem.name,
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable { clickCallBack() },
                    contentScale = ContentScale.Crop,
                    placeholder = painterResource(FileTypeIconUtils.getFileTypeIcon(fileItem)),
                    error = painterResource(FileTypeIconUtils.getFileTypeIcon(fileItem))
                )
            }
            fileItem.isVideo -> {
                AsyncImage(
                    model = ImageRequest.Builder(context)
                        .data(fileItem.file)
                        .crossfade(true)
                        .decoderFactory { result, options, _ -> VideoFrameDecoder(result.source, options) }
                        .build(),
                    contentDescription = fileItem.name,
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable { clickCallBack() },
                    contentScale = ContentScale.Crop,
                    placeholder = painterResource(FileTypeIconUtils.getFileTypeIcon(fileItem)),
                    error = painterResource(FileTypeIconUtils.getFileTypeIcon(fileItem))
                )
            }
            else -> {
                Image(
                    painter = painterResource(FileTypeIconUtils.getFileTypeIcon(fileItem)),
                    contentDescription = fileItem.name,
                    modifier = Modifier.size(50.dp)
                )
            }
        }
    }
}

/**
 * Folder style list item component (from the image)
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FolderStyleListItem(
    fileItem: FileItem,
    isSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onCheckboxClick: (Boolean) -> Unit,
    iconClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = onClick,
                onLongClick = onLongClick,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
            .padding(vertical = 16.dp, horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {

        FileIcon(fileItem = fileItem,iconClick)

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 8.dp)
        ) {
            Text(
                text = fileItem.name,
                color = colorResource(R.color.text_black),
                fontWeight = FontWeight.Medium,
                lineHeight = 16.sp,
                fontSize = 13.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // Date (using last modified)
            val dateFormat = remember { SimpleDateFormat("MMM d, yyyy", Locale.getDefault()) }
            val formattedDate = remember(fileItem.lastModified) {
                dateFormat.format(Date(fileItem.lastModified))
            }

            Text(
                text = formattedDate,
                fontWeight = FontWeight.Medium,
                color = Color(0xFFA3A3A3),
                fontSize = 12.sp
            )
        }

        Text(
            text = FileUtils.formatFileSize(fileItem.size),
            fontWeight = FontWeight.Medium,
            color = Color(0xFFA3A3A3),
            fontSize = 12.sp,
            modifier = Modifier.padding(end = 4.dp)
        )

        if (isSelectionMode){
            CustomCheckbox(
                checked = isSelected,
                onCheckedChange = onCheckboxClick,
                size = 18.dp
            )
        }
    }
}