package com.example.clean0522.ui.features.antivirus

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.data.antivirus.CloudScanManager
import com.example.clean0522.data.antivirus.ScanProgress
import com.example.clean0522.data.local.database.AppDatabase
import com.example.clean0522.data.local.entity.IgnoredThreatEntity
import com.example.clean0522.domain.model.ScanResult
import com.example.clean0522.domain.model.ScanState
import com.example.clean0522.domain.model.ThreatInfo
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import androidx.core.net.toUri

/**
 * ViewModel for antivirus scanning functionality
 */
class AntivirusScanViewModel(
    private val context: Context
) : ViewModel() {

    companion object {
        private const val TAG = "AntivirusScanViewModel"
    }

    private val database = AppDatabase.getInstance(context)
    private val ignoredThreatDao = database.ignoredThreatDao()
    private val cloudScanManager = CloudScanManager(context)

    private val _scanResult = MutableStateFlow(ScanResult())
    val scanResult: StateFlow<ScanResult> = _scanResult.asStateFlow()

    private val _currentScanType = MutableStateFlow("")
    val currentScanType: StateFlow<String> = _currentScanType.asStateFlow()

    private val _currentScreen = MutableStateFlow(AntivirusScreen.SCAN_RESULT)
    val currentScreen: StateFlow<AntivirusScreen> = _currentScreen.asStateFlow()

    private val _ignoredThreats = MutableStateFlow<List<ThreatInfo>>(emptyList())
    val ignoredThreats: StateFlow<List<ThreatInfo>> = _ignoredThreats.asStateFlow()

    private val _showDeleteDialog = MutableStateFlow<ThreatInfo?>(null)
    val showDeleteDialog: StateFlow<ThreatInfo?> = _showDeleteDialog.asStateFlow()

    private var currentScanJob: Job? = null
    private var allDetectedThreats: List<ThreatInfo> = emptyList() // Store all threats before filtering

    init {
        // Initialize CloudScan SDK
        if (!cloudScanManager.initialize()) {
            logE("Failed to initialize CloudScan SDK")
        }

        // Load ignored threats
        loadIgnoredThreats()
    }

    /**
     * Start scanning based on scan type
     */
    fun startScan(scanType: String, folderPath: String? = null) {
        // Cancel any existing scan
        currentScanJob?.cancel()

        _currentScanType.value = scanType
        _scanResult.value = ScanResult(scanState = ScanState.SCANNING)

        currentScanJob = when (scanType) {
            "quick" -> startQuickScan()
            "folder" -> startFolderScan(folderPath ?: "")
            "complete" -> startCompleteScan()
            else -> {
                Log.w(TAG, "Unknown scan type: $scanType")
                startQuickScan()
            }
        }
    }

    /**
     * Start quick scan using CloudScan SDK
     */
    private fun startQuickScan(): Job {
        return viewModelScope.launch {
            try {
                cloudScanManager.startQuickScan().collect { progress ->
                    handleScanProgress(progress)
                }
            } catch (e: Exception) {
                logE(TAG, "Error during quick scan", e)
                _scanResult.value = ScanResult(
                    scanState = ScanState.ERROR,
                    errorMessage = e.message
                )
            }
        }
    }

    /**
     * Start folder scan using CloudScan SDK
     */
    private fun startFolderScan(folderPath: String): Job {
        return viewModelScope.launch {
            try {
                cloudScanManager.startFolderScan(folderPath).collect { progress ->
                    handleScanProgress(progress)
                }
            } catch (e: Exception) {
                logE(TAG, "Error during folder scan", e)
                _scanResult.value = ScanResult(
                    scanState = ScanState.ERROR,
                    errorMessage = e.message
                )
            }
        }
    }

    /**
     * Start comprehensive scan using CloudScan SDK
     */
    private fun startCompleteScan(): Job {
        return viewModelScope.launch {
            try {
                cloudScanManager.startComprehensiveScan().collect { progress ->
                    handleScanProgress(progress)
                }
            } catch (e: Exception) {
                logE(TAG, "Error during comprehensive scan", e)
                _scanResult.value = ScanResult(
                    scanState = ScanState.ERROR,
                    errorMessage = e.message
                )
            }
        }
    }

    /**
     * Handle scan progress updates from CloudScan SDK
     */
    private suspend fun handleScanProgress(progress: ScanProgress) {
        when (progress) {
            is ScanProgress.Started -> {
                logD(TAG, "Scan started")
                _scanResult.value = _scanResult.value.copy(
                    scanState = ScanState.SCANNING,
                    progress = 0
                )
            }

            is ScanProgress.Progress -> {
                logD(TAG, "Scan progress: ${progress.percentage}% (${progress.current}/${progress.total})")
                _scanResult.value = _scanResult.value.copy(
                    progress = progress.percentage,
                    totalScanned = progress.total
                )
            }

            is ScanProgress.Completed -> {
                logD(TAG, "Scan completed with ${progress.threats.size} threats found")
                // Store all detected threats before filtering
                allDetectedThreats = progress.threats

                // Filter out ignored threats
                val filteredThreats = filterIgnoredThreats(progress.threats)

                _scanResult.value = ScanResult(
                    totalScanned = _scanResult.value.totalScanned,
                    threatsFound = filteredThreats,
                    scanState = ScanState.COMPLETED,
                    progress = 100
                )
            }

            is ScanProgress.Error -> {
                logE("Scan error: ${progress.errorMessage} (code: ${progress.errorCode})")
                _scanResult.value = ScanResult(
                    scanState = ScanState.ERROR,
                    errorMessage = progress.errorMessage
                )
            }

            is ScanProgress.Cancelled -> {
                logD(TAG, "Scan cancelled")
                _scanResult.value = _scanResult.value.copy(
                    scanState = ScanState.CANCELLED
                )
            }

            is ScanProgress.Interrupted -> {
                logD(TAG, "Scan interrupted")
                _scanResult.value = ScanResult(
                    scanState = ScanState.ERROR,
                    errorMessage = "Scan was interrupted"
                )
            }
        }
    }

    /**
     * Filter out ignored threats
     */
    private suspend fun filterIgnoredThreats(threats: List<ThreatInfo>): List<ThreatInfo> {
        return threats.filter { threat ->
            ignoredThreatDao.isIgnored(threat.id) == 0
        }
    }

    /**
     * Ignore a threat
     */
    fun ignoreThreat(threat: ThreatInfo) {
        viewModelScope.launch {
            val ignoredThreat = IgnoredThreatEntity(
                id = threat.id,
                appName = threat.appName,
                packageName = threat.packageName,
                filePath = threat.filePath,
                md5 = threat.md5,
                virusName = threat.virusName,
                category = threat.category,
                score = threat.score
            )

            ignoredThreatDao.insertIgnoredThreat(ignoredThreat)

            // Remove from current scan results
            val currentThreats = _scanResult.value.threatsFound.toMutableList()
            currentThreats.removeAll { it.id == threat.id }
            _scanResult.value = _scanResult.value.copy(threatsFound = currentThreats)
        }
    }

    /**
     * Delete/uninstall a threat
     */
    fun deleteThreat(threat: ThreatInfo) {
        if (threat.isInstalledApp && threat.packageName != null) {
            navigateToAppSettings(threat.packageName)
        } else if (threat.filePath != null) {
            _showDeleteDialog.value = threat
        } else {
            Log.w(TAG, "Cannot delete threat: no file path")
        }
    }

    /**
     * Hide delete confirmation dialog
     */
    fun hideDeleteDialog() {
        _showDeleteDialog.value = null
    }

    /**
     * Confirm file deletion
     */
    fun confirmDeleteFile(threat: ThreatInfo) {
        viewModelScope.launch {
            try {
                if (threat.filePath != null && deleteFile(threat.filePath)) {
                    // Remove from current scan results and all detected threats
                    removeThreaFromResults(threat)
                    logD(TAG, "Successfully deleted file: ${threat.filePath}")
                }
            } catch (e: Exception) {
                logE(TAG, "Error deleting file: ${threat.filePath}", e)
            } finally {
                _showDeleteDialog.value = null
            }
        }
    }

    /**
     * Check if threats still exist and remove deleted ones
     */
    fun checkAndRemoveDeletedThreats() {
        viewModelScope.launch {
            val currentThreats = _scanResult.value.threatsFound.toMutableList()
            val threatsToRemove = mutableListOf<ThreatInfo>()

            for (threat in currentThreats) {
                val stillExists = if (threat.isInstalledApp && threat.packageName != null) {
                    isAppInstalled(threat.packageName)
                } else if (threat.filePath != null) {
                    java.io.File(threat.filePath).exists()
                } else {
                    true
                }

                if (!stillExists) {
                    threatsToRemove.add(threat)
                    logD(TAG, "Threat no longer exists, removing: ${threat.id}")
                }
            }

            if (threatsToRemove.isNotEmpty()) {
                currentThreats.removeAll(threatsToRemove)
                _scanResult.value = _scanResult.value.copy(threatsFound = currentThreats)

                allDetectedThreats = allDetectedThreats.filter { threat ->
                    !threatsToRemove.any { it.id == threat.id }
                }

                logD(TAG, "Removed ${threatsToRemove.size} deleted threats from results")
            }
        }
    }

    /**
     * Remove threat from results (helper method)
     */
    private fun removeThreaFromResults(threat: ThreatInfo) {
        val currentThreats = _scanResult.value.threatsFound.toMutableList()
        currentThreats.removeAll { it.id == threat.id }
        _scanResult.value = _scanResult.value.copy(threatsFound = currentThreats)

        allDetectedThreats = allDetectedThreats.filter { it.id != threat.id }
    }

    /**
     * Check if an app is still installed
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Navigate to app settings for uninstall
     */
    private fun navigateToAppSettings(packageName: String): Boolean {
        return try {
            val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = "package:$packageName".toUri()
                flags = android.content.Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            logE(TAG, "Failed to open app settings for $packageName", e)
            false
        }
    }

    /**
     * Delete a file
     */
    private fun deleteFile(filePath: String): Boolean {
        return try {
            val file = java.io.File(filePath)
            if (file.exists()) {
                val deleted = file.delete()
                logD(TAG, "File deletion result for $filePath: $deleted")
                deleted
            } else {
                Log.w(TAG, "File does not exist: $filePath")
                false
            }
        } catch (e: Exception) {
            logE(TAG, "Failed to delete file: $filePath", e)
            false
        }
    }

    /**
     * Cancel current scan
     */
    fun cancelScan() {
        logD(TAG, "Cancelling current scan")
        currentScanJob?.cancel()
        cloudScanManager.cancelScan()
        _scanResult.value = _scanResult.value.copy(scanState = ScanState.CANCELLED)
    }


    /**
     * Navigate to ignore list screen
     */
    fun navigateToIgnoreList() {
        _currentScreen.value = AntivirusScreen.IGNORE_LIST
    }

    /**
     * Navigate back to scan result screen
     */
    fun navigateToScanResult() {
        _currentScreen.value = AntivirusScreen.SCAN_RESULT
    }

    /**
     * Load ignored threats from database
     */
    private fun loadIgnoredThreats() {
        viewModelScope.launch {
            try {
                ignoredThreatDao.getAllIgnoredThreats().collect { entities ->
                    val threats = entities.map { entity ->
                        convertEntityToThreatInfo(entity)
                    }
                    _ignoredThreats.value = threats
                    logD(TAG, "Loaded ${threats.size} ignored threats")

                    updateScanResultsWithIgnoredChanges()
                }
            } catch (e: Exception) {
                logE(TAG, "Error loading ignored threats", e)
                _ignoredThreats.value = emptyList()
            }
        }
    }

    /**
     * Remove a threat from ignore list (unignore)
     */
    fun unignoreThreat(threat: ThreatInfo) {
        viewModelScope.launch {
            try {
                ignoredThreatDao.deleteIgnoredThreatById(threat.id)
                logD(TAG, "Unignored threat: ${threat.id}")
            } catch (e: Exception) {
                logE(TAG, "Error unignoring threat: ${threat.id}", e)
            }
        }
    }

    /**
     * Update scan results when ignored threats change
     */
    private fun updateScanResultsWithIgnoredChanges() {
        if (allDetectedThreats.isNotEmpty()) {
            viewModelScope.launch {
                val filteredThreats = filterIgnoredThreats(allDetectedThreats)
                _scanResult.value = _scanResult.value.copy(threatsFound = filteredThreats)
            }
        }
    }

    /**
     * Convert database entity to ThreatInfo model
     */
    private fun convertEntityToThreatInfo(entity: IgnoredThreatEntity): ThreatInfo {
        return ThreatInfo(
            id = entity.id,
            appName = entity.appName,
            packageName = entity.packageName,
            filePath = entity.filePath,
            md5 = entity.md5,
            score = entity.score,
            virusName = entity.virusName,
            category = entity.category,
            summary = arrayOf(), // Summary not stored in database for simplicity
            isFromStatic = true // Default value
        )
    }
}

/**
 * Enum for different screens in antivirus flow
 */
enum class AntivirusScreen {
    SCAN_RESULT,
    IGNORE_LIST
}
