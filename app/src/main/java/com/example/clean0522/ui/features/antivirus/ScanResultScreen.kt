package com.example.clean0522.ui.features.antivirus

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import com.example.clean0522.R
import com.example.clean0522.domain.model.ScanResult
import com.example.clean0522.domain.model.ThreatInfo
import com.example.clean0522.domain.model.ThreatLevel

/**
 * Screen showing scan results
 */
@Composable
fun ScanResultScreen(
    scanResult: ScanResult,
    onIgnoreThreat: (ThreatInfo) -> Unit,
    onDeleteThreat: (ThreatInfo) -> Unit,
    onNavigateToFeature: (String) -> Unit = {},
    onOpenIgnoreList: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val featureRecommendations = getFeatureRecommendations()
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            ScanSummaryCard(
                totalScanned = scanResult.totalScanned,
                threatsFound = scanResult.threatCount
            )
        }

        if (scanResult.threatsFound.isNotEmpty()) {
            item {
                Text(
                    text = stringResource(R.string.delete_or_ignore_threats),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            items(scanResult.threatsFound) { threat ->
                ThreatItemCard(
                    threat = threat,
                    onIgnoreClick = { onIgnoreThreat(threat) },
                    onDeleteClick = { onDeleteThreat(threat) }
                )
            }
        } else {
            item {
                NoThreatsFoundCard()
            }

            // Feature recommendations when no threats found
            item {
                Text(
                    text = stringResource(R.string.here_are_some_other_features),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            }

            items(featureRecommendations) { feature ->
                FeatureRecommendationCard(
                    feature = feature,
                    onFeatureClick = { onNavigateToFeature(feature.id) }
                )
            }
        }
    }
}

/**
 * Card showing scan summary
 */
@Composable
private fun ScanSummaryCard(
    totalScanned: Int,
    threatsFound: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row (
            modifier = Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            Image(painter = painterResource(R.drawable.ic_antivirus),
                contentDescription = null,
                modifier = Modifier.size(95.dp))

            Spacer(modifier = Modifier.height(16.dp))

            Column {
                // Scan Results
                Text(
                    text = stringResource(R.string.items_scanned, totalScanned),
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = stringResource(R.string.threats_found, threatsFound),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (threatsFound > 0)
                        MaterialTheme.colorScheme.error
                    else
                        MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}

/**
 * Card for individual threat item
 */
@Composable
private fun ThreatItemCard(
    threat: ThreatInfo,
    onIgnoreClick: () -> Unit,
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                if (threat.isInstalledApp && threat.packageName != null) {
                    AppIconImage(
                        packageName = threat.packageName,
                        modifier = Modifier.size(50.dp)
                    )
                } else {
                    Image(
                        painter = painterResource(R.drawable.ic_files),
                        contentDescription = null,
                        modifier = Modifier.size(50.dp)
                    )
                }

                Spacer(modifier = Modifier.width(4.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = threat.displayName,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                        )

                        Text(
                            text = if (threat.isInstalledApp) threat.displayPath else threat.displayPath,
                            fontSize = 12.sp,
                            lineHeight = 12.sp,
                            maxLines = 2,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier
                        )

                        Text(
                            text = threat.virusName,
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    ThreatLevelBadge(threatLevel = threat.threatLevel)
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Action Buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {

                Button(
                    onClick = onIgnoreClick,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Blue
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        stringResource(R.string.ignore),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }

                Button(
                    onClick = onDeleteClick,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        stringResource(
                            if (threat.isInstalledApp) R.string.uninstall else R.string.delete
                        ),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

/**
 * Badge showing threat level
 */
@Composable
fun ThreatLevelBadge(
    threatLevel: ThreatLevel
) {
    val (backgroundColor, textColor, text) = when (threatLevel) {
        ThreatLevel.MALWARE -> Triple(
            Color.Red.copy(alpha = 0.1f),
            Color.Red,
            stringResource(R.string.threat_level_malware)
        )
        ThreatLevel.PUA -> Triple(
            Color(0xFFFF9800).copy(alpha = 0.1f),
            Color(0xFFFF9800),
            stringResource(R.string.threat_level_pua)
        )
        ThreatLevel.SAFE -> Triple(
            Color.Green.copy(alpha = 0.1f),
            Color.Green,
            stringResource(R.string.threat_level_safe)
        )
    }

    Text(
        text = text,
        fontSize = 14.sp,
        fontWeight = FontWeight.SemiBold,
        color = textColor
    )
}

/**
 * Card shown when no threats are found
 */
@Composable
private fun NoThreatsFoundCard(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(R.string.device_is_secure),
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.primary
            )

            Text(
                text = stringResource(R.string.no_threats_found),
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

/**
 * Data class for feature recommendations
 */
data class FeatureRecommendation(
    val id: String,
    val title: String,
    val icon: ImageVector,
    val iconColor: Color
)

/**
 * Get list of feature recommendations
 */
@Composable
private fun getFeatureRecommendations(): List<FeatureRecommendation> {
    return listOf(
        FeatureRecommendation(
            id = "device_storage",
            title = stringResource(R.string.device_storage),
            icon = Icons.Default.Storage,
            iconColor = Color(0xFF4CAF50)
        ),
        FeatureRecommendation(
            id = "security_scan",
            title = stringResource(R.string.security_scan),
            icon = Icons.Default.Security,
            iconColor = Color(0xFF2196F3)
        ),
        FeatureRecommendation(
            id = "recent_files",
            title = stringResource(R.string.utility_recent_files),
            icon = Icons.Default.AccessTime,
            iconColor = Color(0xFFFF9800)
        ),
        FeatureRecommendation(
            id = "large_files",
            title = stringResource(R.string.utility_large_files),
            icon = Icons.Default.Folder,
            iconColor = Color(0xFFFF5722)
        ),
        FeatureRecommendation(
            id = "duplicate_files",
            title = stringResource(R.string.utility_duplicate_files),
            icon = Icons.Default.ContentCopy,
            iconColor = Color(0xFF00BCD4)
        ),
        FeatureRecommendation(
            id = "redundant_files",
            title = stringResource(R.string.utility_redundant_files),
            icon = Icons.Default.Delete,
            iconColor = Color(0xFF9C27B0)
        ),
        FeatureRecommendation(
            id = "ram_usage",
            title = stringResource(R.string.ram_usage),
            icon = Icons.Default.PhoneAndroid,
            iconColor = Color(0xFFE91E63)
        ),
        FeatureRecommendation(
            id = "battery_info",
            title = stringResource(R.string.battery_info),
            icon = Icons.Default.BatteryAlert,
            iconColor = Color(0xFF4CAF50)
        )
    )
}

/**
 * Card for individual feature recommendation
 */
@Composable
private fun FeatureRecommendationCard(
    feature: FeatureRecommendation,
    onFeatureClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { onFeatureClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Feature Icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(feature.iconColor.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = feature.icon,
                    contentDescription = feature.title,
                    modifier = Modifier.size(24.dp),
                    tint = feature.iconColor
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Feature Title
            Text(
                text = feature.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )

            // Arrow Icon
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Component to display app icon for installed apps
 */
@Composable
fun AppIconImage(
    packageName: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val appIcon = remember(packageName) {
        try {
            context.packageManager.getApplicationIcon(packageName)
        } catch (e: Exception) {
            null
        }
    }

    if (appIcon != null) {
        Image(
            painter = BitmapPainter(appIcon.toBitmap().asImageBitmap()),
            contentDescription = "App icon",
            modifier = modifier
                .size(24.dp)
                .clip(RoundedCornerShape(4.dp))
        )
    } else {
        // Fallback to default app icon
        Icon(
            imageVector = Icons.Default.Android,
            contentDescription = "App icon",
            modifier = modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}