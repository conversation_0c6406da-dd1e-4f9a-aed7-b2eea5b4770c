package com.example.clean0522.ui.features.utility

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.ui.components.*
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.R

/**
 * Large files content composable
 */
@Composable
fun LargeFilesContent(viewModel: FileUtilityViewModel) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadLargeFiles()
    }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // File type tabs
        FileTypeTabRow(
            selectedType = viewModel.selectedLargeFileType,
            onTypeSelected = { viewModel.selectLargeFileType(it) }
        )

        Spacer(modifier = Modifier.height(12.dp))

        // File list
        if (viewModel.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (viewModel.filteredLargeFiles.isEmpty()) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Image(painter = painterResource(R.mipmap.empty_file),
                    contentDescription = null,
                    modifier = Modifier.size(150.dp))

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = stringResource(R.string.empty_folder),
                    fontSize = 14.sp,
                    color = colorResource(R.color.text_gray_70),
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RoundedCornerShape(15.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                LazyColumn(
                    modifier = Modifier
                ) {
                    items(viewModel.filteredLargeFiles) { fileItem ->
                        FileListItem(
                            fileItem = fileItem,
                            isSelectionMode = viewModel.isSelectionMode,
                            isSelected = viewModel.selectedFiles.contains(fileItem),
                            onClick = { viewModel.onFileClick(context,fileItem) },
                            onLongClick = { viewModel.onFileLongClick(fileItem) },
                            onCheckboxClick = { viewModel.toggleFileSelection(fileItem) }
                        )

                        if (fileItem != viewModel.filteredLargeFiles.last()){
                            Divider(
                                modifier = Modifier.padding(horizontal = 16.dp),
                                thickness = 0.5.dp
                            )
                        }
                    }
                }
            }
        }
    }

    // Dialogs
    if (viewModel.showDetailsDialog && viewModel.currentSelectedFile != null) {
        FileDetailsDialog(
            fileItem = viewModel.currentSelectedFile!!,
            onDismiss = { viewModel.dismissFileDetails() }
        )
    }

    if (viewModel.showRenameDialog && viewModel.currentSelectedFile != null) {
        RenameFileDialog(
            currentName = viewModel.currentSelectedFile!!.name,
            onDismiss = { viewModel.dismissRenameFile() },
            onConfirm = { newName -> viewModel.renameFile(newName) }
        )
    }

    if (viewModel.showDeleteDialog) {
        DeleteConfirmationDialog(
            onDismiss = { viewModel.dismissDeleteConfirmation() },
            onConfirm = { viewModel.deleteSelectedFiles() }
        )
    }

    if (viewModel.showSortDialog) {
        SortDialog(
            currentSortOption = viewModel.currentSortOption,
            onSortOptionSelected = { viewModel.setSortOption(it) },
            onDismiss = { viewModel.dismissSortDialog() }
        )
    }
}

/**
 * Recent files content composable
 */
@Composable
fun RecentFilesContent(viewModel: FileUtilityViewModel) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadRecentFiles()
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        if (viewModel.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (viewModel.recentFiles.isEmpty()) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Image(painter = painterResource(R.mipmap.empty_file),
                    contentDescription = null,
                    modifier = Modifier.size(150.dp))

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = stringResource(R.string.empty_folder),
                    fontSize = 14.sp,
                    color = colorResource(R.color.text_gray_70),
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RoundedCornerShape(15.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                LazyColumn(
                    modifier = Modifier
                ) {
                    items(viewModel.recentFiles) { fileItem ->
                        FileListItem(
                            fileItem = fileItem,
                            isSelectionMode = viewModel.isSelectionMode,
                            isSelected = viewModel.selectedFiles.contains(fileItem),
                            onClick = { viewModel.onFileClick(context,fileItem) },
                            onLongClick = { viewModel.onFileLongClick(fileItem) },
                            onCheckboxClick = { viewModel.toggleFileSelection(fileItem) },
                            iconClick = { FileUtils.openFile(context, fileItem.file) }
                        )
                        if (fileItem != viewModel.recentFiles.last()){
                            Divider(
                                modifier = Modifier.padding(horizontal = 16.dp),
                                thickness = 0.5.dp
                            )
                        }
                    }
                }
            }
        }
    }

    // Dialogs
    if (viewModel.showDetailsDialog && viewModel.currentSelectedFile != null) {
        FileDetailsDialog(
            fileItem = viewModel.currentSelectedFile!!,
            onDismiss = { viewModel.dismissFileDetails() }
        )
    }

    if (viewModel.showRenameDialog && viewModel.currentSelectedFile != null) {
        RenameFileDialog(
            currentName = viewModel.currentSelectedFile!!.name,
            onDismiss = { viewModel.dismissRenameFile() },
            onConfirm = { newName -> viewModel.renameFile(newName) }
        )
    }

    if (viewModel.showDeleteDialog) {
        DeleteConfirmationDialog(
            onDismiss = { viewModel.dismissDeleteConfirmation() },
            onConfirm = { viewModel.deleteSelectedFiles() }
        )
    }

    if (viewModel.showSortDialog) {
        println("DEBUG - Showing SortDialog in RecentFilesContent")
        SortDialog(
            currentSortOption = viewModel.currentSortOption,
            onSortOptionSelected = { viewModel.setSortOption(it) },
            onDismiss = { viewModel.dismissSortDialog() }
        )
    }
}

/**
 * Duplicate files content composable
 */
@Composable
fun DuplicateFilesContent(viewModel: FileUtilityViewModel) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadDuplicateFiles()
        viewModel.changeSelectMode(true)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // File groups list
        if (viewModel.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (viewModel.duplicateFileGroups.isEmpty()) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Image(painter = painterResource(R.mipmap.empty_file),
                    contentDescription = null,
                    modifier = Modifier.size(120.dp))

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = "No large files found")
            }
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f)
            ) {
                items(viewModel.duplicateFileGroups) { group ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                        shape = RoundedCornerShape(15.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        )
                    ) {
                        FileGroupItem(
                            group = group,
                            isExpanded = viewModel.expandedGroups.contains(group.id),
                            isSelectionMode = viewModel.isSelectionMode,
                            isGroupSelected = viewModel.selectedGroups.contains(group.id),
                            selectedFiles = viewModel.selectedFiles,
                            onGroupClick = { viewModel.toggleGroupExpansion(group.id) },
                            onGroupLongClick = { viewModel.onFileLongClick(group.files.first()) },
                            onGroupSelectionToggle = { viewModel.toggleGroupSelection(group) },
                            onFileClick = { viewModel.onFileClick(context,it) },
                            onFileLongClick = { viewModel.onFileLongClick(it) },
                            onFileSelectionToggle = { viewModel.toggleFileSelection(it) },
                            showExpandIcon = true,
                            showGroupSelection = true
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }

    // Dialogs
    if (viewModel.showDetailsDialog && viewModel.currentSelectedFile != null) {
        FileDetailsDialog(
            fileItem = viewModel.currentSelectedFile!!,
            onDismiss = { viewModel.dismissFileDetails() }
        )
    }

    if (viewModel.showRenameDialog && viewModel.currentSelectedFile != null) {
        RenameFileDialog(
            currentName = viewModel.currentSelectedFile!!.name,
            onDismiss = { viewModel.dismissRenameFile() },
            onConfirm = { newName -> viewModel.renameFile(newName) }
        )
    }

    if (viewModel.showDeleteDialog) {
        DeleteConfirmationDialog(
            onDismiss = { viewModel.dismissDeleteConfirmation() },
            onConfirm = { viewModel.deleteSelectedFiles() }
        )
    }
}