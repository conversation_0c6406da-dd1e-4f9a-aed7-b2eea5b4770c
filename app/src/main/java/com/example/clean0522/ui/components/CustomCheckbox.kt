package com.example.clean0522.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.clean0522.R

/**
 * Custom checkbox component using custom icons
 */
@Composable
fun CustomCheckbox(
    checked: Boolean,
    onCheckedChange: ((Boolean) -> Unit)?,
    modifier: Modifier = Modifier,
    size: Dp = 18.dp,
    checkedIcon: Int = R.mipmap.ck_check,
    uncheckedIcon: Int = R.mipmap.ck_en_all,
    enabled: Boolean = true
) {
    val iconRes = if (checked) checkedIcon else uncheckedIcon
    
    Box(
        modifier = modifier
            .size(size)
            .clickable(
                enabled = enabled,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ) {
                onCheckedChange?.invoke(!checked)
            },
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = iconRes),
            contentDescription = if (checked) "Checked" else "Unchecked",
            modifier = Modifier.size(size)
        )
    }
}
