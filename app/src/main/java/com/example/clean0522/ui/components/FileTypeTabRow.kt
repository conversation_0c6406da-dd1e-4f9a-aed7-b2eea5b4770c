package com.example.clean0522.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.domain.model.LargeFileType
import com.example.clean0522.R

/**
 * File type tab row for filtering large files
 */
@Composable
fun FileTypeTabRow(
    selectedType: LargeFileType,
    onTypeSelected: (LargeFileType) -> Unit,
    modifier: Modifier = Modifier
) {
    val types = LargeFileType.values()
    Column {
        Row(
            modifier = modifier
                .fillMaxWidth(),
        ) {
            types.forEach { type ->
                FileTypeTab(
                    type = type,
                    isSelected = selectedType == type,
                    onClick = { onTypeSelected(type) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
        Spacer(modifier = Modifier.height(1.dp)
            .fillMaxWidth()
            .background(colorResource(R.color.text_gray)))
    }
}

/**
 * Individual file type tab
 */
@Composable
private fun FileTypeTab(
    type: LargeFileType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                if (isSelected) {
                    colorResource(R.color.text_blue)
                } else {
                    Color.Transparent
                }
                , shape = RoundedCornerShape(topStart = 7.dp, topEnd = 7.dp)
            )
            .clickable { onClick() }
            .padding(vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = type.displayName,
            fontWeight = FontWeight.SemiBold,
            color = if (isSelected) {
                Color.White
            } else {
                Color(0xFFA3A3A3)
            },
            fontSize = 12.sp
        )
    }
}
