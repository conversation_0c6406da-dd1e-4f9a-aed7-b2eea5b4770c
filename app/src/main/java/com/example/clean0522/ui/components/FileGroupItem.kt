package com.example.clean0522.ui.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.filled.Image
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.example.clean0522.R
import com.example.clean0522.domain.model.FileGroup
import com.example.clean0522.domain.model.FileItem
import com.example.clean0522.ui.components.CustomCheckbox
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.utils.FileTypeIconUtils

/**
 * File group item component for displaying grouped files
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FileGroupItem(
    group: FileGroup,
    isExpanded: Boolean = false,
    isSelectionMode: Boolean = false,
    isGroupSelected: Boolean = false,
    selectedFiles: Set<FileItem> = emptySet(),
    onGroupClick: () -> Unit = {},
    onGroupLongClick: () -> Unit = {},
    onGroupSelectionToggle: () -> Unit = {},
    onFileClick: (FileItem) -> Unit = {},
    onFileLongClick: (FileItem) -> Unit = {},
    onFileSelectionToggle: (FileItem) -> Unit = {},
    showExpandIcon: Boolean = true,
    showGroupSelection: Boolean = false
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.Center
    ) {
        // Group header
        val context = LocalContext.current
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .combinedClickable(
                    onClick = onGroupClick,
                    onLongClick = onGroupLongClick,
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                )
                .padding(16.dp)
                .background(Color.White)
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            if (showExpandIcon) {
                IconButton(
                    onClick = onGroupClick,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (isExpanded) "Collapse" else "Expand"
                    )
                }
            }

            // Group information
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 4.dp)
            ) {
                Text(
                    text = group.name,
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 16.sp,
                    color = colorResource(R.color.text_black),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Text(
                text = FileUtils.formatFileSize(group.totalSize),
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFFA3A3A3),
                fontSize = 12.sp
            )

            // Group selection checkbox
            if (showGroupSelection && isSelectionMode) {
                CustomCheckbox(
                    checked = isGroupSelected,
                    onCheckedChange = { onGroupSelectionToggle() },
                    modifier = Modifier.padding(start = 8.dp),
                    size = 18.dp
                )
            }

        }

        // Expanded file list
        if (isExpanded) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                group.files.forEach { fileItem ->
                    FileListItem(
                        fileItem = fileItem,
                        isSelectionMode = isSelectionMode,
                        isSelected = selectedFiles.contains(fileItem),
                        onClick = { onFileClick(fileItem) },
                        onLongClick = { onFileLongClick(fileItem) },
                        onCheckboxClick = { onFileSelectionToggle(fileItem) },
                        iconClick = { FileUtils.openFile(context, fileItem.file) }
                    )

                    Divider(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        thickness = 0.5.dp
                    )
                }
            }
        }
    }
}

/**
 * Similar photos group item with grid layout
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun SimilarPhotosGroupItem(
    group: FileGroup,
    isSelectionMode: Boolean = false,
    selectedFiles: Set<FileItem> = emptySet(),
    onFileClick: (FileItem) -> Unit = {},
    onFileLongClick: (FileItem) -> Unit = {},
    onFileSelectionToggle: (FileItem) -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        // Group title
        Text(
            text = group.name,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = colorResource(R.color.text_black),
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Photo grid (2x2 or 2x3 layout)
        val chunkedPhotos = group.files.chunked(4)

        chunkedPhotos.forEach { rowPhotos ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                rowPhotos.forEach { photo ->
                    PhotoThumbnailItem(
                        photo = photo,
                        isSelected = isSelectionMode && selectedFiles.contains(photo),
                        onFileClick = onFileClick,
                        onFileLongClick = onFileLongClick,
                        onFileSelectionToggle = onFileSelectionToggle,
                        modifier = Modifier.weight(1f)
                    )
                }

                // Fill remaining space if row is not complete
                repeat(4 - rowPhotos.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }

            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

/**
 * Photo thumbnail item component for displaying image thumbnails
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PhotoThumbnailItem(
    photo: FileItem,
    isSelected: Boolean = false,
    onFileClick: (FileItem) -> Unit = {},
    onFileLongClick: (FileItem) -> Unit = {},
    onFileSelectionToggle: (FileItem) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    Box(
        modifier = modifier
            .aspectRatio(1f)
            .clip(RoundedCornerShape(8.dp))
            .combinedClickable(
                onClick = { onFileClick(photo) },
                onLongClick = { onFileLongClick(photo) },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        // Image thumbnail
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(photo.file)
                .crossfade(true)
                .build(),
            contentDescription = photo.name,
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFEEEEEE)),
            contentScale = ContentScale.Crop,
            placeholder = painterResource(R.mipmap.empty_image),
            error = painterResource(R.mipmap.empty_image)
        )

        // Selection overlay
        if (isSelected) {
            Box(
                modifier = Modifier
                    .fillMaxSize(),
                contentAlignment = Alignment.BottomEnd
            ) {
                CustomCheckbox(
                    checked = true,
                    onCheckedChange = { onFileSelectionToggle(photo) },
                    modifier = Modifier.padding(4.dp),
                    size = 20.dp
                )
            }
        }
    }
}