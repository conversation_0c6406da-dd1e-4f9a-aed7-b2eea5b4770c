package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.utils.RamUsageUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for RAM usage monitoring
 */
class RamUsageViewModel : ViewModel() {

    private val _ramUsageInfo = MutableStateFlow<RamUsageUtils.RamUsageInfo?>(null)
    val ramUsageInfo: StateFlow<RamUsageUtils.RamUsageInfo?> = _ramUsageInfo.asStateFlow()

    private val _chartDataManager = RamUsageUtils.ChartDataManager()
    val chartDataManager: RamUsageUtils.ChartDataManager get() = _chartDataManager

    private val _isMonitoring = MutableStateFlow(false)
    val isMonitoring: StateFlow<Boolean> = _isMonitoring.asStateFlow()

    /**
     * Start monitoring RAM usage
     */
    fun startMonitoring(context: Context) {
        if (_isMonitoring.value) return

        _isMonitoring.value = true
        
        viewModelScope.launch {
            RamUsageUtils.getRamUsageFlow(context).collect { ramUsage ->
                if (_isMonitoring.value) {
                    _ramUsageInfo.value = ramUsage
                    _chartDataManager.addDataPoint(ramUsage)
                }
            }
        }
    }

    /**
     * Stop monitoring RAM usage
     */
    fun stopMonitoring() {
        _isMonitoring.value = false
    }

    override fun onCleared() {
        super.onCleared()
        stopMonitoring()
    }
}
