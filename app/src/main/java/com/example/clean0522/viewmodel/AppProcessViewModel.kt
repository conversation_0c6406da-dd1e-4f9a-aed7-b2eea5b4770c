package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.utils.AppProcessUtils
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for app process monitoring
 */
class AppProcessViewModel : ViewModel() {

    private val _appProcessSummary = MutableStateFlow<AppProcessUtils.AppProcessSummary?>(null)
    val appProcessSummary: StateFlow<AppProcessUtils.AppProcessSummary?> = _appProcessSummary.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _isMonitoring = MutableStateFlow(false)
    val isMonitoring: StateFlow<Boolean> = _isMonitoring.asStateFlow()

    private val _stoppedApps = MutableStateFlow<Set<String>>(emptySet())
    val stoppedApps: StateFlow<Set<String>> = _stoppedApps.asStateFlow()

    /**
     * Start monitoring app processes
     */
    fun startMonitoring(context: Context) {
        if (_isMonitoring.value) return

        _isMonitoring.value = true
        _isLoading.value = true

        logD("AppProcessViewModel", "Starting app process monitoring")

        viewModelScope.launch {
            try {
                AppProcessUtils.getAppProcessSummaryFlow(context).collect { summary ->
                    if (_isMonitoring.value) {
                        _appProcessSummary.value = summary
                        _isLoading.value = false

                        // Also check stopped apps status periodically
                        checkStoppedAppsStatus(context)

                        logD("AppProcessViewModel", "Updated app process summary: ${summary.runningAppsCount} apps")
                    }
                }
            } catch (e: Exception) {
                logE("Error in app process monitoring", e)
                _isLoading.value = false
            }
        }
    }

    /**
     * Stop monitoring app processes
     */
    fun stopMonitoring() {
        logD("AppProcessViewModel", "Stopping app process monitoring")
        _isMonitoring.value = false
    }

    /**
     * Stop an app by opening its settings
     */
    fun stopApp(context: Context, packageName: String) {
        logD("AppProcessViewModel", "Attempting to stop app: $packageName")

        viewModelScope.launch {
            try {
                AppProcessUtils.openAppSettings(context, packageName)
            } catch (e: Exception) {
                logE("Error stopping app: $packageName", e)
            }
        }
    }

    /**
     * Open app settings and check app status when returning
     */
    fun openAppSettings(context: Context, packageName: String) {
        logD("AppProcessViewModel", "Opening app settings for: $packageName")

        viewModelScope.launch {
            try {
                AppProcessUtils.openAppSettings(context, packageName)
                // Mark this app as potentially stopped for UI update
                _stoppedApps.value = _stoppedApps.value + packageName
            } catch (e: Exception) {
                logE("Error opening app settings: $packageName", e)
            }
        }
    }

    /**
     * Check and update app running status when returning from settings
     */
    fun checkAppStatus(context: Context, packageName: String) {
        logD("AppProcessViewModel", "Checking app status for: $packageName")

        viewModelScope.launch {
            try {
                val isAppStopped = AppProcessUtils.isAppStopped(context, packageName)
                val currentStoppedApps = _stoppedApps.value.toMutableSet()

                if (isAppStopped) {
                    // App is stopped, keep it in stopped list
                    currentStoppedApps.add(packageName)
                    logD("AppProcessViewModel", "App $packageName is stopped")
                } else {
                    // App is still running, remove from stopped list
                    currentStoppedApps.remove(packageName)
                    logD("AppProcessViewModel", "App $packageName is still running")
                }

                _stoppedApps.value = currentStoppedApps
            } catch (e: Exception) {
                logE("Error checking app status: $packageName", e)
            }
        }
    }

    /**
     * Check status of all stopped apps (used during periodic monitoring)
     */
    private fun checkStoppedAppsStatus(context: Context) {
        if (_stoppedApps.value.isEmpty()) return

        viewModelScope.launch {
            try {
                val currentStoppedApps = _stoppedApps.value.toMutableSet()
                val appsToCheck = currentStoppedApps.toList()

                for (packageName in appsToCheck) {
                    val isAppStopped = AppProcessUtils.isAppStopped(context, packageName)
                    if (!isAppStopped) {
                        // App is running again, remove from stopped list
                        currentStoppedApps.remove(packageName)
                        logD("AppProcessViewModel", "App $packageName is running again during periodic check")
                    }
                }

                if (currentStoppedApps != _stoppedApps.value) {
                    _stoppedApps.value = currentStoppedApps
                }
            } catch (e: Exception) {
                logE("Error checking stopped apps status", e)
            }
        }
    }

    /**
     * Check if an app is in the stopped list
     */
    fun isAppStopped(packageName: String): Boolean {
        return _stoppedApps.value.contains(packageName)
    }

    /**
     * Refresh app process data and update stopped apps status
     */
    fun refresh(context: Context) {
        logD("AppProcessViewModel", "Refreshing app process data")

        viewModelScope.launch {
            try {
                _isLoading.value = true
                val summary = AppProcessUtils.getAppProcessSummary(context)
                _appProcessSummary.value = summary

                // Update stopped apps status for all apps in the stopped list
                val currentStoppedApps = _stoppedApps.value.toMutableSet()
                val appsToCheck = currentStoppedApps.toList() // Create a copy to iterate

                for (packageName in appsToCheck) {
                    val isAppStopped = AppProcessUtils.isAppStopped(context, packageName)
                    if (isAppStopped) {
                        // App is still stopped, keep it in the list
                        currentStoppedApps.add(packageName)
                    } else {
                        // App is no longer stopped, remove from list
                        currentStoppedApps.remove(packageName)
                        logD("AppProcessViewModel", "App $packageName is running again, removing from stopped list")
                    }
                }

                _stoppedApps.value = currentStoppedApps
                _isLoading.value = false
                logD("AppProcessViewModel", "Refreshed app process data successfully")
            } catch (e: Exception) {
                logE("Error refreshing app process data", e)
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear stopped apps list
     */
    fun clearStoppedApps() {
        _stoppedApps.value = emptySet()
    }

    override fun onCleared() {
        super.onCleared()
        stopMonitoring()
    }
}
