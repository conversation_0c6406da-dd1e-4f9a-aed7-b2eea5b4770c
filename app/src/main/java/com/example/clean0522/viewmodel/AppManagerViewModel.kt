package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.domain.model.SortOption
import com.example.clean0522.utils.AppManagerUtils
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.async

/**
 * ViewModel for App Manager functionality
 */
class AppManagerViewModel : ViewModel() {

    private val _allApps = MutableStateFlow<List<AppManagerUtils.AppInfo>>(emptyList())
    val allApps: StateFlow<List<AppManagerUtils.AppInfo>> = _allApps.asStateFlow()

    private val _systemApps = MutableStateFlow<List<AppManagerUtils.AppInfo>>(emptyList())
    val systemApps: StateFlow<List<AppManagerUtils.AppInfo>> = _systemApps.asStateFlow()

    private val _installedApps = MutableStateFlow<List<AppManagerUtils.AppInfo>>(emptyList())
    val installedApps: StateFlow<List<AppManagerUtils.AppInfo>> = _installedApps.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _currentSortOption = MutableStateFlow(SortOption.NAME_A_TO_Z)
    val currentSortOption: StateFlow<SortOption> = _currentSortOption.asStateFlow()

    private val _showSortDialog = MutableStateFlow(false)
    val showSortDialog: StateFlow<Boolean> = _showSortDialog.asStateFlow()

    private val _allAppsCount = MutableStateFlow(0)
    val allAppsCount: StateFlow<Int> = _allAppsCount.asStateFlow()

    private val _systemAppsCount = MutableStateFlow(0)
    val systemAppsCount: StateFlow<Int> = _systemAppsCount.asStateFlow()

    private val _installedAppsCount = MutableStateFlow(0)
    val installedAppsCount: StateFlow<Int> = _installedAppsCount.asStateFlow()

    /**
     * Load all apps data
     */
    fun loadApps(context: Context) {
        if (_isLoading.value) return

        logD("AppManagerViewModel", "Loading apps data")
        _isLoading.value = true

        viewModelScope.launch {
            try {
                // Load all apps in parallel
                val allAppsDeferred = async(kotlinx.coroutines.Dispatchers.IO) { AppManagerUtils.getAllApps(context, AppManagerUtils.AppFilter.ALL) }
                val systemAppsDeferred = async(kotlinx.coroutines.Dispatchers.IO) { AppManagerUtils.getAllApps(context, AppManagerUtils.AppFilter.SYSTEM_APPS) }
                val installedAppsDeferred = async(kotlinx.coroutines.Dispatchers.IO) { AppManagerUtils.getAllApps(context, AppManagerUtils.AppFilter.INSTALLED_APPS) }

                val allAppsList = allAppsDeferred.await()
                val systemAppsList = systemAppsDeferred.await()
                val installedAppsList = installedAppsDeferred.await()

                // Apply current sorting
                _allApps.value = sortApps(allAppsList)
                _systemApps.value = sortApps(systemAppsList)
                _installedApps.value = sortApps(installedAppsList)

                // Update counts
                _allAppsCount.value = allAppsList.size
                _systemAppsCount.value = systemAppsList.size
                _installedAppsCount.value = installedAppsList.size

                logD("AppManagerViewModel", "Loaded apps - All: ${allAppsList.size}, System: ${systemAppsList.size}, Installed: ${installedAppsList.size}")
            } catch (e: Exception) {
                logE("Error loading apps", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Update sort option and re-sort all lists
     */
    fun updateSortOption(sortOption: SortOption) {
        logD("AppManagerViewModel", "Updating sort option to: $sortOption")
        _currentSortOption.value = sortOption

        // Re-sort all current lists
        _allApps.value = sortApps(_allApps.value)
        _systemApps.value = sortApps(_systemApps.value)
        _installedApps.value = sortApps(_installedApps.value)
    }

    /**
     * Show sort dialog
     */
    fun showSortDialog() {
        _showSortDialog.value = true
    }

    /**
     * Hide sort dialog
     */
    fun hideSortDialog() {
        _showSortDialog.value = false
    }

    /**
     * Open app details
     */
    fun openAppDetails(context: Context, packageName: String) {
        logD("AppManagerViewModel", "Opening app details for: $packageName")
        viewModelScope.launch {
            try {
                AppManagerUtils.openAppDetails(context, packageName)
            } catch (e: Exception) {
                logE("Error opening app details for $packageName", e)
            }
        }
    }

    /**
     * Refresh apps data
     */
    fun refreshApps(context: Context) {
        logD("AppManagerViewModel", "Refreshing apps data")
        loadApps(context)
    }

    /**
     * Sort apps based on current sort option
     */
    private fun sortApps(apps: List<AppManagerUtils.AppInfo>): List<AppManagerUtils.AppInfo> {
        return when (_currentSortOption.value) {
            SortOption.NAME_A_TO_Z -> apps.sortedBy { it.appName.lowercase() }
            SortOption.NAME_Z_TO_A -> apps.sortedByDescending { it.appName.lowercase() }
            SortOption.NEWEST_FIRST -> apps.sortedByDescending { it.updateTime }
            SortOption.OLDEST_FIRST -> apps.sortedBy { it.updateTime }
            SortOption.SMALL_TO_LARGE -> apps.sortedBy { it.size }
            SortOption.LARGE_TO_SMALL -> apps.sortedByDescending { it.size }
        }
    }
}
