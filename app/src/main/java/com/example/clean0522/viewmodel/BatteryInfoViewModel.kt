package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.utils.BatteryInfoUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for battery information monitoring
 */
class BatteryInfoViewModel : ViewModel() {

    private val _batteryInfo = MutableStateFlow<BatteryInfoUtils.BatteryInfo?>(null)
    val batteryInfo: StateFlow<BatteryInfoUtils.BatteryInfo?> = _batteryInfo.asStateFlow()

    private val _isMonitoring = MutableStateFlow(false)
    val isMonitoring: StateFlow<Boolean> = _isMonitoring.asStateFlow()

    /**
     * Start monitoring battery information
     */
    fun startMonitoring(context: Context) {
        if (_isMonitoring.value) return

        _isMonitoring.value = true
        
        viewModelScope.launch {
            BatteryInfoUtils.getBatteryInfoFlow(context).collect { batteryInfo ->
                if (_isMonitoring.value) {
                    _batteryInfo.value = batteryInfo
                }
            }
        }
    }

    /**
     * Stop monitoring battery information
     */
    fun stopMonitoring() {
        _isMonitoring.value = false
    }

    override fun onCleared() {
        super.onCleared()
        stopMonitoring()
    }
}
