package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.utils.NetworkInfoUtils
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for network information monitoring
 */
class NetworkInfoViewModel : ViewModel() {

    private val _networkInfo = MutableStateFlow<NetworkInfoUtils.NetworkInfo?>(null)
    val networkInfo: StateFlow<NetworkInfoUtils.NetworkInfo?> = _networkInfo.asStateFlow()

    private val _isMonitoring = MutableStateFlow(false)
    val isMonitoring: StateFlow<Boolean> = _isMonitoring.asStateFlow()

    private var monitoringJob: Job? = null

    /**
     * Start monitoring network information
     */
    fun startMonitoring(context: Context) {
        if (_isMonitoring.value) {
            logD("NetworkInfoViewModel", "Monitoring already started")
            return
        }

        logD("NetworkInfoViewModel", "Starting network monitoring")
        _isMonitoring.value = true

        monitoringJob = viewModelScope.launch {
            try {
                while (_isMonitoring.value) {
                    val networkInfo = NetworkInfoUtils.getCurrentNetworkInfo(context)
                    _networkInfo.value = networkInfo
                    
                    logD("NetworkInfoViewModel", "Network info updated - WiFi connected: ${networkInfo.wifiInfo.isConnected}")
                    
                    delay(5000)
                }
            } catch (e: Exception) {
                logE("Error in network monitoring", e)
            }
        }
    }

    /**
     * Stop monitoring network information
     */
    fun stopMonitoring() {
        logD("NetworkInfoViewModel", "Stopping network monitoring")
        _isMonitoring.value = false
        monitoringJob?.cancel()
        monitoringJob = null
    }

    /**
     * Refresh network information immediately
     */
    fun refreshNetworkInfo(context: Context) {
        viewModelScope.launch {
            try {
                val networkInfo = NetworkInfoUtils.getCurrentNetworkInfo(context)
                _networkInfo.value = networkInfo
                logD("NetworkInfoViewModel", "Network info refreshed manually")
            } catch (e: Exception) {
                logE("Error refreshing network info", e)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        stopMonitoring()
    }
}
