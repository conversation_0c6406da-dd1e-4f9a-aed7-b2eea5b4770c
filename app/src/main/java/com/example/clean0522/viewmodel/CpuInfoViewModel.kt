package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.utils.CpuInfoUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * ViewModel for CPU information monitoring
 */
class CpuInfoViewModel : ViewModel() {

    data class GpuInfo(
        val renderer: String,
        val vendor: String,
        val version: String,
        val vulkanVersion: String
    )

    private val _cpuInfo = MutableStateFlow<CpuInfoUtils.CpuInfo?>(null)
    val cpuInfo: StateFlow<CpuInfoUtils.CpuInfo?> = _cpuInfo.asStateFlow()

    // GPU信息状态
    val _gpuRenderer = MutableStateFlow("Unknown")
    val gpuRenderer: StateFlow<String> = _gpuRenderer.asStateFlow()

    val _gpuVendor = MutableStateFlow("Unknown")
    val gpuVendor: StateFlow<String> = _gpuVendor.asStateFlow()

    val _gpuVersion = MutableStateFlow("Unknown")
    val gpuVersion: StateFlow<String> = _gpuVersion.asStateFlow()

    private val _vulkanVersion = MutableStateFlow("Unknown")
    val vulkanVersion: StateFlow<String> = _vulkanVersion.asStateFlow()

    private val _isMonitoring = MutableStateFlow(false)
    val isMonitoring: StateFlow<Boolean> = _isMonitoring.asStateFlow()

    /**
     * Start monitoring CPU information
     */
    fun startMonitoring(context: Context) {
        if (_isMonitoring.value) return

        _isMonitoring.value = true

        // Initialize Vulkan version
        viewModelScope.launch {
            _vulkanVersion.value = getVulkanVersion(context)
        }

        // Monitor CPU info continuously
        viewModelScope.launch {
            CpuInfoUtils.getCpuInfoFlow().collect { cpuInfo ->
                if (_isMonitoring.value) {
                    _cpuInfo.value = cpuInfo
                }
            }
        }
    }

    /**
     * Get combined GPU info for display
     */
    fun getGpuInfo(): GpuInfo {
        return GpuInfo(
            renderer = _gpuRenderer.value,
            vendor = _gpuVendor.value,
            version = _gpuVersion.value,
            vulkanVersion = _vulkanVersion.value
        )
    }

    /**
     * Stop monitoring CPU information
     */
    fun stopMonitoring() {
        _isMonitoring.value = false
    }

    override fun onCleared() {
        super.onCleared()
        stopMonitoring()
    }

    /**
     * Get Vulkan version information
     */
    private fun getVulkanVersion(context: Context): String {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                val packageManager = context.packageManager

                // Check for specific Vulkan versions using PackageManager
                when {
                    // Check for Vulkan 1.3 (API level 0x403000)
                    packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_VULKAN_HARDWARE_VERSION, 0x403000) -> "Vulkan 1.3"
                    // Check for Vulkan 1.2 (API level 0x402000)
                    packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_VULKAN_HARDWARE_VERSION, 0x402000) -> "Vulkan 1.2"
                    // Check for Vulkan 1.1 (API level 0x401000)
                    packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_VULKAN_HARDWARE_VERSION, 0x401000) -> "Vulkan 1.1"
                    // Check for Vulkan 1.0 (API level 0x400000)
                    packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_VULKAN_HARDWARE_VERSION, 0x400000) -> "Vulkan 1.0"
                    // Check for basic Vulkan support
                    packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_VULKAN_HARDWARE_LEVEL) -> "Vulkan 1.0"
                    else -> "Not Supported"
                }
            } else {
                "Not Supported"
            }
        } catch (e: Exception) {
            // Fallback based on Android version
            when {
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R -> "Vulkan 1.1" // Android 11+
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N -> "Vulkan 1.0" // Android 7+
                else -> "Not Supported"
            }
        }
    }
}
