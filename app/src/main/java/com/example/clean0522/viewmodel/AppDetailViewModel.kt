package com.example.clean0522.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.utils.AppDetailUtils
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for app detail screen
 */
class AppDetailViewModel : ViewModel() {

    private val _appDetailInfo = MutableStateFlow<AppDetailUtils.AppDetailInfo?>(null)
    val appDetailInfo: StateFlow<AppDetailUtils.AppDetailInfo?> = _appDetailInfo.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    /**
     * Load app detail information
     */
    fun loadAppDetail(context: Context, packageName: String) {
        logD("AppDetailViewModel", "Loading app detail for: $packageName")

        viewModelScope.launch(Dispatchers.IO) {
            _isLoading.value = true
            _error.value = null

            try {
                // Debug certificate extraction
                AppDetailUtils.debugCertificates(context, packageName)

                val appDetail = AppDetailUtils.getAppDetailInfo(context, packageName)
                _appDetailInfo.value = appDetail

                if (appDetail == null) {
                    _error.value = "Failed to load app information"
                } else {
                    logD("AppDetailViewModel", "Successfully loaded app detail for: $packageName, certificates: ${appDetail.certificates.size}")
                }
            } catch (e: Exception) {
                logE("Error loading app detail for $packageName", e)
                _error.value = "Error loading app information: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear error state
     */
    fun clearError() {
        _error.value = null
    }
}
