package com.example.clean0522

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.List
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.activity.ComponentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.domain.model.ScanState
import com.example.clean0522.domain.model.ThreatInfo
import com.example.clean0522.ui.features.antivirus.AntivirusScanViewModel
import com.example.clean0522.ui.features.antivirus.AntivirusScreen
import com.example.clean0522.ui.features.antivirus.IgnoreListScreen
import com.example.clean0522.ui.features.antivirus.ScanProgressScreen
import com.example.clean0522.ui.features.antivirus.ScanResultScreen
import com.example.clean0522.ui.theme.Clean0522Theme

/**
 * Activity for antivirus scanning
 */
class AntivirusScanActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_SCAN_TYPE = "scan_type"
        const val SCAN_TYPE_QUICK = "quick"
        const val SCAN_TYPE_FOLDER = "folder"
        const val SCAN_TYPE_COMPLETE = "complete"

        fun createIntent(context: Context, scanType: String): Intent {
            return Intent(context, AntivirusScanActivity::class.java).apply {
                putExtra(EXTRA_SCAN_TYPE, scanType)
            }
        }
    }

    @Composable
    override fun setHomePage() {
        val scanType = intent.getStringExtra(EXTRA_SCAN_TYPE) ?: SCAN_TYPE_QUICK
        val folderPath = intent.getStringExtra("folder_path")

        Clean0522Theme {
            AntivirusScanScreen(
                scanType = scanType,
                folderPath = folderPath,
                onBackClick = { finish() },
                onNavigateToFeature = { featureId ->
                    navigateToFeature(featureId)
                }
            )
        }
    }


    /**
     * Navigate to different features based on feature ID
     */
    fun navigateToFeature(featureId: String) {
        val intent = when (featureId) {
            "device_storage" -> {
                Intent(this, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
            }
            "security_scan" -> {
                Intent(this, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                    putExtra("navigate_to", "antivirus")
                }
            }
            "recent_files" -> {
                Intent(this, FileUtilityActivity::class.java).apply {
                    putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, "recent_files")
                }
            }
            "large_files" -> {
                Intent(this, FileUtilityActivity::class.java).apply {
                    putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, "large_files")
                }
            }
            "duplicate_files" -> {
                Intent(this, FileUtilityActivity::class.java).apply {
                    putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, "duplicate_files")
                }
            }
            "redundant_files" -> {
                Intent(this, FileUtilityActivity::class.java).apply {
                    putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, "redundant_files")
                }
            }
            "ram_usage" -> {
                Intent(this, SystemInfoActivity::class.java).apply {
                    putExtra("info_type", "ram_usage")
                }
            }
            "battery_info" -> {
                Intent(this, SystemInfoActivity::class.java).apply {
                    putExtra("info_type", "battery_info")
                }
            }
            else -> {
                Intent(this, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
            }
        }
        startActivity(intent)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AntivirusScanScreen(
    scanType: String,
    folderPath: String?,
    onBackClick: () -> Unit,
    onNavigateToFeature: (String) -> Unit = {}
) {
    val viewModel: AntivirusScanViewModel = viewModel { AntivirusScanViewModel(CleanApp.appContext) }
    val scanResult by viewModel.scanResult.collectAsState()
    val currentScreen by viewModel.currentScreen.collectAsState()
    val ignoredThreats by viewModel.ignoredThreats.collectAsState()
    val showDeleteDialog by viewModel.showDeleteDialog.collectAsState()

    // Check for deleted threats when returning to the screen
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        // Use a lifecycle observer to check when returning from other activities
        (context as? ComponentActivity)?.lifecycle?.addObserver(object : androidx.lifecycle.DefaultLifecycleObserver {
            override fun onResume(owner: androidx.lifecycle.LifecycleOwner) {
                viewModel.checkAndRemoveDeletedThreats()
            }
        })
    }

    val title = when (currentScreen) {
        AntivirusScreen.IGNORE_LIST -> stringResource(R.string.ignore_list)
        AntivirusScreen.SCAN_RESULT -> when (scanType) {
            AntivirusScanActivity.SCAN_TYPE_QUICK -> stringResource(R.string.antivirus_quick_scan)
            AntivirusScanActivity.SCAN_TYPE_FOLDER -> stringResource(R.string.antivirus_folder_scan)
            AntivirusScanActivity.SCAN_TYPE_COMPLETE -> stringResource(R.string.antivirus_complete_scan)
            else -> stringResource(R.string.antivirus_quick_scan)
        }
    }

    // Start scan when screen is first displayed
    LaunchedEffect(scanType) {
        viewModel.startScan(scanType, folderPath)
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        topBar = {
            Row(
                modifier = Modifier
                    .padding(horizontal = 12.dp, vertical = 4.dp)
                    .statusBarsPadding()
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = {
                    when (currentScreen) {
                        AntivirusScreen.IGNORE_LIST -> {
                            viewModel.navigateToScanResult()
                        }
                        AntivirusScreen.SCAN_RESULT -> {
                            if (scanResult.scanState == ScanState.SCANNING) {
                                viewModel.cancelScan()
                            }
                            onBackClick()
                        }
                    }
                }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = stringResource(R.string.back),
                        modifier = Modifier.size(20.dp)
                    )
                }
                Text(
                    text = title,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)
                )
                if (currentScreen == AntivirusScreen.SCAN_RESULT && scanResult.scanState == ScanState.COMPLETED) {
                    IconButton(
                        onClick = {
                            viewModel.navigateToIgnoreList()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.List,
                            contentDescription = stringResource(R.string.ignore_list),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                } else {
                    Spacer(modifier = Modifier.size(20.dp))
                }
            }
        }
    ) { innerPadding ->
        when (currentScreen) {
            AntivirusScreen.SCAN_RESULT -> {
                when (scanResult.scanState) {
                    ScanState.IDLE, ScanState.SCANNING -> {
                        ScanProgressScreen(
                            scanResult = scanResult,
                            scanType = scanType,
                            onCancelClick = {
                                viewModel.cancelScan()
                                onBackClick()
                            },
                            modifier = Modifier.padding(innerPadding)
                        )
                    }
                    ScanState.COMPLETED -> {
                        ScanResultScreen(
                            scanResult = scanResult,
                            onIgnoreThreat = { threat ->
                                viewModel.ignoreThreat(threat)
                            },
                            onDeleteThreat = { threat ->
                                viewModel.deleteThreat(threat)
                            },
                            onNavigateToFeature = onNavigateToFeature,
                            modifier = Modifier.padding(innerPadding)
                        )
                    }
                    ScanState.ERROR, ScanState.CANCELLED -> {
                        ScanProgressScreen(
                            scanResult = scanResult,
                            scanType = scanType,
                            onCancelClick = onBackClick,
                            modifier = Modifier.padding(innerPadding)
                        )
                    }
                }
            }
            AntivirusScreen.IGNORE_LIST -> {
                IgnoreListScreen(
                    ignoredThreats = ignoredThreats,
                    onUnignoreThreat = { threat ->
                        viewModel.unignoreThreat(threat)
                    },
                    modifier = Modifier.padding(innerPadding)
                )
            }
        }

        // Delete confirmation dialog
        showDeleteDialog?.let { threat ->
            DeleteConfirmationDialog(
                threat = threat,
                onConfirm = {
                    viewModel.confirmDeleteFile(threat)
                },
                onDismiss = {
                    viewModel.hideDeleteDialog()
                }
            )
        }
    }
}

/**
 * Dialog for confirming file deletion
 */
@Composable
private fun DeleteConfirmationDialog(
    threat: ThreatInfo,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.delete_file_title),
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = stringResource(R.string.delete_file_message),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Text(
                    text = threat.displayName,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text(stringResource(R.string.delete))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        }
    )
}