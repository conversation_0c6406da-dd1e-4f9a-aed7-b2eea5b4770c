package com.example.clean0522.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.DhcpInfo
import android.net.NetworkCapabilities
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Build
import android.telephony.TelephonyManager
import com.example.clean0522.R
import java.net.Inet4Address
import java.net.InterfaceAddress
import java.net.NetworkInterface
import java.util.Collections

/**
 * Utility class for retrieving network information
 */
object NetworkInfoUtils {

    /**
     * Data class for WiFi information
     */
    data class WiFiInfo(
        val isConnected: Boolean,
        val ssid: String,
        val dhcpEnabled: String,
        val dhcpLeaseDuration: String,
        val dns1: String,
        val dns2: String,
        val frequency: String,
        val gateway: String,
        val networkInterface: String,
        val ipAddress: String,
        val ipv6Address: String,
        val linkSpeed: String,
        val netmask: String,
        val quality: String,
        val safety: String,
        val strength: String,
        val wifiDirectSupported: String
    )

    /**
     * Data class for Mobile Data information
     */
    data class MobileDataInfo(
        val isMultiSimSupported: String,
        val isDataEnabled: String,
        val networkType: String,
        val operatorName: String,
        val countryCode: String,
        val networkCode: String
    )

    /**
     * Data class for complete network information
     */
    data class NetworkInfo(
        val wifiInfo: WiFiInfo,
        val mobileDataInfo: MobileDataInfo,
        val timestamp: Long = System.currentTimeMillis()
    )

    /**
     * Get current network information
     */
    fun getCurrentNetworkInfo(context: Context): NetworkInfo {
        val wifiInfo = getWiFiInfo(context)
        val mobileDataInfo = getMobileDataInfo(context)

        return NetworkInfo(
            wifiInfo = wifiInfo,
            mobileDataInfo = mobileDataInfo
        )
    }

    /**
     * Get WiFi information
     */
    @SuppressLint("MissingPermission")
    private fun getWiFiInfo(context: Context): WiFiInfo {
        return try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            val isWifiConnected = isWiFiConnected(connectivityManager)

            if (!isWifiConnected || !wifiManager.isWifiEnabled) {
                return getDisconnectedWiFiInfo()
            }

            val wifiInfo = wifiManager.connectionInfo
            val dhcpInfo = wifiManager.dhcpInfo

            WiFiInfo(
                isConnected = true,
                ssid = getSSID(wifiInfo),
                dhcpEnabled = if (dhcpInfo != null) context.getString(R.string.network_enabled) else context.getString(R.string.network_disabled),
                dhcpLeaseDuration = getDhcpLeaseDuration(dhcpInfo, context),
                dns1 = getDns1(dhcpInfo),
                dns2 = getDns2(dhcpInfo),
                frequency = getFrequency(wifiInfo, context),
                gateway = getGateway(dhcpInfo),
                networkInterface = getNetworkInterface(),
                ipAddress = getIpAddress(dhcpInfo),
                ipv6Address = getIpv6Address(),
                linkSpeed = getLinkSpeed(wifiInfo, context),
                netmask = getNetmask(dhcpInfo),
                quality = getQuality(wifiInfo, context),
                safety = getSafety(wifiInfo, context),
                strength = getStrength(wifiInfo, context),
                wifiDirectSupported = getWifiDirectSupport(wifiManager, context)
            )
        } catch (e: Exception) {
            logE("Error getting WiFi info", e)
            getDisconnectedWiFiInfo()
        }
    }

    /**
     * Get mobile data information
     */
    @SuppressLint("MissingPermission")
    private fun getMobileDataInfo(context: Context): MobileDataInfo {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            MobileDataInfo(
                isMultiSimSupported = getMultiSimSupport(telephonyManager, context),
                isDataEnabled = getMobileDataStatus(connectivityManager, context),
                networkType = getNetworkType(telephonyManager),
                operatorName = getOperatorName(telephonyManager),
                countryCode = getCountryCode(telephonyManager),
                networkCode = getNetworkCode(telephonyManager)
            )
        } catch (e: Exception) {
            logE("Error getting mobile data info", e)
            getUnavailableMobileDataInfo(context)
        }
    }

    /**
     * Check if WiFi is connected
     */
    private fun isWiFiConnected(connectivityManager: ConnectivityManager): Boolean {
        return try {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Get disconnected WiFi info
     */
    private fun getDisconnectedWiFiInfo(): WiFiInfo {
        val unknown = "N/A"
        return WiFiInfo(
            isConnected = false,
            ssid = unknown,
            dhcpEnabled = unknown,
            dhcpLeaseDuration = unknown,
            dns1 = unknown,
            dns2 = unknown,
            frequency = unknown,
            gateway = unknown,
            networkInterface = unknown,
            ipAddress = unknown,
            ipv6Address = unknown,
            linkSpeed = unknown,
            netmask = unknown,
            quality = unknown,
            safety = unknown,
            strength = unknown,
            wifiDirectSupported = unknown
        )
    }

    /**
     * Get unavailable mobile data info
     */
    private fun getUnavailableMobileDataInfo(context: Context): MobileDataInfo {
        val unknown = "N/A"
        return MobileDataInfo(
            isMultiSimSupported = unknown,
            isDataEnabled = unknown,
            networkType = unknown,
            operatorName = unknown,
            countryCode = unknown,
            networkCode = unknown
        )
    }

    /**
     * Get SSID from WiFi info
     */
    private fun getSSID(wifiInfo: WifiInfo?): String {
        return try {
            val ssid = wifiInfo?.ssid
            if (ssid != null && ssid != "<unknown ssid>" && ssid.isNotEmpty()) {
                // Remove quotes if present
                ssid.replace("\"", "")
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get DHCP lease duration
     */
    private fun getDhcpLeaseDuration(dhcpInfo: DhcpInfo?, context: Context): String {
        return try {
            if (dhcpInfo != null && dhcpInfo.leaseDuration > 0) {
                context.getString(R.string.unit_seconds, dhcpInfo.leaseDuration)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get DNS1 address
     */
    private fun getDns1(dhcpInfo: DhcpInfo?): String {
        return try {
            if (dhcpInfo != null && dhcpInfo.dns1 != 0) {
                intToIp(dhcpInfo.dns1)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get DNS2 address
     */
    private fun getDns2(dhcpInfo: DhcpInfo?): String {
        return try {
            if (dhcpInfo != null && dhcpInfo.dns2 != 0) {
                intToIp(dhcpInfo.dns2)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get WiFi frequency
     */
    private fun getFrequency(wifiInfo: WifiInfo?, context: Context): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && wifiInfo != null) {
                val frequency = wifiInfo.frequency
                if (frequency > 0) {
                    context.getString(R.string.unit_mhz, frequency)
                } else {
                    "N/A"
                }
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get gateway address
     */
    private fun getGateway(dhcpInfo: DhcpInfo?): String {
        return try {
            if (dhcpInfo != null && dhcpInfo.gateway != 0) {
                intToIp(dhcpInfo.gateway)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get network interface
     */
    private fun getNetworkInterface(): String {
        return try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (networkInterface in interfaces) {
                if (networkInterface.name.startsWith("wlan")) {
                    return networkInterface.name
                }
            }
            "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get IP address
     */
    private fun getIpAddress(dhcpInfo: DhcpInfo?): String {
        return try {
            if (dhcpInfo != null && dhcpInfo.ipAddress != 0) {
                intToIp(dhcpInfo.ipAddress)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get IPv6 address
     */
    private fun getIpv6Address(): String {
        return try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (networkInterface in interfaces) {
                if (networkInterface.name.startsWith("wlan")) {
                    val addresses = Collections.list(networkInterface.inetAddresses)
                    for (address in addresses) {
                        if (!address.isLoopbackAddress && address !is Inet4Address) {
                            return address.hostAddress ?: "N/A"
                        }
                    }
                }
            }
            "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get link speed
     */
    private fun getLinkSpeed(wifiInfo: WifiInfo?, context: Context): String {
        return try {
            if (wifiInfo != null) {
                val linkSpeed = wifiInfo.linkSpeed
                if (linkSpeed > 0) {
                    context.getString(R.string.unit_mbps, linkSpeed)
                } else {
                    "N/A"
                }
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get netmask using NetworkInterface (permission-free method)
     */
    private fun getNetmask(dhcpInfo: DhcpInfo?): String {
        return try {
            val netmaskFromInterface = getNetmaskFromNetworkInterface()
            if (netmaskFromInterface != "N/A") {
                return netmaskFromInterface
            }

            if (dhcpInfo != null && dhcpInfo.netmask != 0) {
                intToIp(dhcpInfo.netmask)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get netmask from NetworkInterface using getNetworkPrefixLength (permission-free)
     */
    private fun getNetmaskFromNetworkInterface(): String {
        return try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (networkInterface in interfaces) {
                // Look for WiFi interface (usually wlan0)
                if (networkInterface.name.startsWith("wlan") && networkInterface.isUp) {
                    val interfaceAddresses = networkInterface.interfaceAddresses
                    for (interfaceAddress in interfaceAddresses) {
                        val address = interfaceAddress.address
                        // Check if it's an IPv4 address and not loopback
                        if (address is Inet4Address && !address.isLoopbackAddress) {
                            val prefixLength = interfaceAddress.networkPrefixLength
                            return prefixLengthToSubnetMask(prefixLength.toInt())
                        }
                    }
                }
            }
            "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Convert network prefix length to subnet mask
     * For example: 24 -> *************, 16 -> ***********
     */
    private fun prefixLengthToSubnetMask(prefixLength: Int): String {
        return try {
            if (prefixLength < 0 || prefixLength > 32) {
                return "N/A"
            }

            val mask = (0xFFFFFFFF.toInt() shl (32 - prefixLength))
            val byte1 = (mask ushr 24) and 0xFF
            val byte2 = (mask ushr 16) and 0xFF
            val byte3 = (mask ushr 8) and 0xFF
            val byte4 = mask and 0xFF

            "$byte1.$byte2.$byte3.$byte4"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get WiFi quality (signal level as percentage)
     */
    private fun getQuality(wifiInfo: WifiInfo?, context: Context): String {
        return try {
            if (wifiInfo != null) {
                val rssi = wifiInfo.rssi
                val quality = WifiManager.calculateSignalLevel(rssi, 100)
                context.getString(R.string.unit_percent, quality)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get WiFi safety (security type)
     */
    private fun getSafety(wifiInfo: WifiInfo?, context: Context): String {
        return try {
            if (wifiInfo != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                when (wifiInfo.currentSecurityType) {
                    WifiInfo.SECURITY_TYPE_OPEN -> context.getString(R.string.wifi_security_open)
                    WifiInfo.SECURITY_TYPE_WEP -> context.getString(R.string.wifi_security_wep)
                    WifiInfo.SECURITY_TYPE_PSK -> context.getString(R.string.wifi_security_wpa2)
                    WifiInfo.SECURITY_TYPE_EAP -> context.getString(R.string.wifi_security_wpa2)
                    WifiInfo.SECURITY_TYPE_SAE -> context.getString(R.string.wifi_security_wpa3)
                    WifiInfo.SECURITY_TYPE_EAP_WPA3_ENTERPRISE -> context.getString(R.string.wifi_security_wpa3)
                    WifiInfo.SECURITY_TYPE_EAP_WPA3_ENTERPRISE_192_BIT -> context.getString(R.string.wifi_security_wpa3)
                    WifiInfo.SECURITY_TYPE_OWE -> "OWE"
                    WifiInfo.SECURITY_TYPE_WAPI_PSK -> "WAPI PSK"
                    WifiInfo.SECURITY_TYPE_WAPI_CERT -> "WAPI CERT"
                    WifiInfo.SECURITY_TYPE_PASSPOINT_R1_R2 -> "Passpoint R1/R2"
                    WifiInfo.SECURITY_TYPE_PASSPOINT_R3 -> "Passpoint R3"
                    WifiInfo.SECURITY_TYPE_DPP -> "DPP"
                    WifiInfo.SECURITY_TYPE_OSEN -> "OSEN"
                    WifiInfo.SECURITY_TYPE_UNKNOWN -> context.getString(R.string.wifi_security_unknown)
                    else -> context.getString(R.string.wifi_security_unknown)
                }
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get WiFi strength
     */
    private fun getStrength(wifiInfo: WifiInfo?, context: Context): String {
        return try {
            if (wifiInfo != null) {
                val rssi = wifiInfo.rssi
                context.getString(R.string.unit_dbm, rssi)
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get WiFi Direct support
     */
    private fun getWifiDirectSupport(wifiManager: WifiManager?, context: Context): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                // WiFi Direct is supported from API 14+
                context.getString(R.string.network_supported)
            } else {
                context.getString(R.string.network_not_supported)
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get multi-SIM support
     */
    private fun getMultiSimSupport(telephonyManager: TelephonyManager?, context: Context): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && telephonyManager != null) {
                val simCount = telephonyManager.phoneCount
                if (simCount > 1) {
                    context.getString(R.string.network_supported)
                } else {
                    context.getString(R.string.network_not_supported)
                }
            } else {
                context.getString(R.string.network_not_supported)
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get mobile data status
     */
    private fun getMobileDataStatus(connectivityManager: ConnectivityManager?, context: Context): String {
        return try {
            if (connectivityManager != null) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)

                if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true) {
                    context.getString(R.string.network_connected)
                } else {
                    context.getString(R.string.network_disconnected)
                }
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get network type
     */
    @SuppressLint("MissingPermission")
    private fun getNetworkType(telephonyManager: TelephonyManager?): String {
        return try {
            if (telephonyManager != null) {
                when (telephonyManager.networkType) {
                    TelephonyManager.NETWORK_TYPE_GPRS,
                    TelephonyManager.NETWORK_TYPE_EDGE,
                    TelephonyManager.NETWORK_TYPE_CDMA,
                    TelephonyManager.NETWORK_TYPE_1xRTT,
                    TelephonyManager.NETWORK_TYPE_IDEN -> "2G"

                    TelephonyManager.NETWORK_TYPE_UMTS,
                    TelephonyManager.NETWORK_TYPE_EVDO_0,
                    TelephonyManager.NETWORK_TYPE_EVDO_A,
                    TelephonyManager.NETWORK_TYPE_HSDPA,
                    TelephonyManager.NETWORK_TYPE_HSUPA,
                    TelephonyManager.NETWORK_TYPE_HSPA,
                    TelephonyManager.NETWORK_TYPE_EVDO_B,
                    TelephonyManager.NETWORK_TYPE_EHRPD,
                    TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"

                    TelephonyManager.NETWORK_TYPE_LTE -> "4G"

                    else -> {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            when (telephonyManager.networkType) {
                                TelephonyManager.NETWORK_TYPE_NR -> "5G"
                                else -> "Unknown"
                            }
                        } else {
                            "Unknown"
                        }
                    }
                }
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get operator name
     */
    private fun getOperatorName(telephonyManager: TelephonyManager?): String {
        return try {
            telephonyManager?.networkOperatorName?.takeIf { it.isNotEmpty() } ?: "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get country code
     */
    private fun getCountryCode(telephonyManager: TelephonyManager?): String {
        return try {
            telephonyManager?.networkCountryIso?.takeIf { it.isNotEmpty() }?.uppercase() ?: "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get network code
     */
    private fun getNetworkCode(telephonyManager: TelephonyManager?): String {
        return try {
            telephonyManager?.networkOperator?.takeIf { it.isNotEmpty() } ?: "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Convert integer IP to string format
     */
    private fun intToIp(ip: Int): String {
        return "${ip and 0xFF}.${ip shr 8 and 0xFF}.${ip shr 16 and 0xFF}.${ip shr 24 and 0xFF}"
    }

    /**
     * Get all WiFi info items for display
     */
    fun getWiFiInfoItems(context: Context, wifiInfo: WiFiInfo): List<Pair<String, String>> {
        return listOf(
            context.getString(R.string.wifi_ssid) to wifiInfo.ssid,
            context.getString(R.string.wifi_dhcp) to wifiInfo.dhcpEnabled,
            context.getString(R.string.wifi_dhcp_lease_duration) to wifiInfo.dhcpLeaseDuration,
            context.getString(R.string.wifi_dns_1) to wifiInfo.dns1,
            context.getString(R.string.wifi_dns_2) to wifiInfo.dns2,
            context.getString(R.string.wifi_frequency) to wifiInfo.frequency,
            context.getString(R.string.wifi_gateway) to wifiInfo.gateway,
            context.getString(R.string.wifi_interface) to wifiInfo.networkInterface,
            context.getString(R.string.wifi_ip) to wifiInfo.ipAddress,
            context.getString(R.string.wifi_ipv6) to wifiInfo.ipv6Address,
            context.getString(R.string.wifi_link_speed) to wifiInfo.linkSpeed,
            context.getString(R.string.wifi_netmask) to wifiInfo.netmask,
            context.getString(R.string.wifi_quality) to wifiInfo.quality,
            context.getString(R.string.wifi_safety) to wifiInfo.safety,
            context.getString(R.string.wifi_strength) to wifiInfo.strength,
            context.getString(R.string.wifi_direct) to wifiInfo.wifiDirectSupported
        ).filter { it.second != "N/A" && it.second != "N/A" } // Filter out items with no value
    }

    /**
     * Get all mobile data info items for display
     */
    fun getMobileDataInfoItems(context: Context, mobileDataInfo: MobileDataInfo): List<Pair<String, String>> {
        return listOf(
            context.getString(R.string.mobile_multi_sim) to mobileDataInfo.isMultiSimSupported,
            context.getString(R.string.mobile_status) to mobileDataInfo.isDataEnabled,
            context.getString(R.string.mobile_network_type) to mobileDataInfo.networkType,
            context.getString(R.string.mobile_operator) to mobileDataInfo.operatorName,
            context.getString(R.string.mobile_country_code) to mobileDataInfo.countryCode,
            context.getString(R.string.mobile_network_code) to mobileDataInfo.networkCode
        ).filter { it.second != "N/A" && it.second != "N/A" } // Filter out items with no value
    }
}
