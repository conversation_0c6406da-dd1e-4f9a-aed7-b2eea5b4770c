package com.example.clean0522.utils

import android.content.Context
import android.os.Environment
import com.example.clean0522.domain.model.FileGroup
import com.example.clean0522.domain.model.FileItem
import com.example.clean0522.domain.model.LargeFileType
import com.example.clean0522.domain.model.RedundantFileType
import java.io.File
import java.security.MessageDigest
import java.util.concurrent.TimeUnit

/**
 * File analyzer utility for various file operations
 */
object FileAnalyzer {

    private const val LARGE_FILE_SIZE_THRESHOLD = 10 * 1024 * 1024L // 10MB
    private const val RECENT_DAYS_THRESHOLD = 7L

    /**
     * Find large files (>10MB) in the device
     */
    fun findLargeFiles(): List<FileItem> {
        val largeFiles = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()

        scanDirectoryForLargeFiles(rootDir, largeFiles)

        return largeFiles.sortedByDescending { it.size }
    }

    /**
     * Filter large files by type
     */
    fun filterLargeFilesByType(files: List<FileItem>, type: LargeFileType): List<FileItem> {
        return when (type) {
            LargeFileType.ALL -> files
            LargeFileType.IMAGES -> files.filter { it.isImage }
            LargeFileType.AUDIOS -> files.filter { it.isAudio }
            LargeFileType.VIDEOS -> files.filter { it.isVideo }
            LargeFileType.OTHERS -> files.filter {
                !it.isImage && !it.isAudio && !it.isVideo && !it.isDirectory
            }
        }
    }

    /**
     * Find recently modified files (within 7 days)
     */
    fun findRecentFiles(): List<FileItem> {
        val recentFiles = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()
        val cutoffTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(RECENT_DAYS_THRESHOLD)

        scanDirectoryForRecentFiles(rootDir, recentFiles, cutoffTime)

        return recentFiles.sortedByDescending { it.lastModified }
    }

    /**
     * Find duplicate files by comparing file hashes
     */
    fun findDuplicateFiles(): List<FileGroup> {
        val allFiles = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()

        scanDirectoryForAllFiles(rootDir, allFiles)

        // Group files by hash
        val filesByHash = mutableMapOf<String, MutableList<FileItem>>()

        allFiles.forEach { file ->
            if (!file.isDirectory && file.size > 0) {
                val hash = calculateFileHash(file.file)
                if (hash != null) {
                    filesByHash.getOrPut(hash) { mutableListOf() }.add(file)
                }
            }
        }

        // Return only groups with duplicates
        return filesByHash.values
            .filter { it.size > 1 }
            .mapIndexed { index, files ->
                FileGroup.fromFiles("Group ${index + 1}", files)
            }
    }

    /**
     * Find redundant files by category
     */
    fun findRedundantFiles(): List<FileGroup> {
        val redundantGroups = mutableListOf<FileGroup>()

        // APKs
        val apkFiles = findFilesByExtension(listOf("apk"))
        if (apkFiles.isNotEmpty()) {
            redundantGroups.add(FileGroup.fromFiles(RedundantFileType.APKS.displayName, apkFiles))
        }

        // Download Files
        val downloadFiles = findFilesInDirectory("Download")
        if (downloadFiles.isNotEmpty()) {
            redundantGroups.add(FileGroup.fromFiles(RedundantFileType.DOWNLOAD_FILES.displayName, downloadFiles))
        }

        // Screenshots
        val screenshotFiles = findScreenshotFiles()
        if (screenshotFiles.isNotEmpty()) {
            redundantGroups.add(FileGroup.fromFiles(RedundantFileType.SCREENSHOTS.displayName, screenshotFiles))
        }

        // Log Files
        val logFiles = findFilesByExtension(listOf("log", "txt"))
            .filter { it.name.contains("log", ignoreCase = true) }
        if (logFiles.isNotEmpty()) {
            redundantGroups.add(FileGroup.fromFiles(RedundantFileType.LOG_FILES.displayName, logFiles))
        }

        // Temp Files
        val tempFiles = findTempFiles()
        if (tempFiles.isNotEmpty()) {
            redundantGroups.add(FileGroup.fromFiles(RedundantFileType.TEMP_FILES.displayName, tempFiles))
        }

        return redundantGroups
    }

    /**
     * Find similar photos using enhanced detection algorithms
     * Uses MediaStore for fast scanning and multiple algorithms for accurate detection
     */
    suspend fun findSimilarPhotos(context: Context): List<FileGroup> {
        return EnhancedSimilarImageDetector.findSimilarPhotos(context)
    }

    private fun scanDirectoryForLargeFiles(directory: File, largeFiles: MutableList<FileItem>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    scanDirectoryForLargeFiles(file, largeFiles)
                } else if (file.length() > LARGE_FILE_SIZE_THRESHOLD) {
                    largeFiles.add(FileItem(file))
                }
            }
        } catch (e: Exception) {
            // Handle permission errors
        }
    }

    private fun scanDirectoryForRecentFiles(directory: File, recentFiles: MutableList<FileItem>, cutoffTime: Long) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    scanDirectoryForRecentFiles(file, recentFiles, cutoffTime)
                } else if (file.lastModified() > cutoffTime) {
                    recentFiles.add(FileItem(file))
                }
            }
        } catch (e: Exception) {
            // Handle permission errors
        }
    }

    private fun scanDirectoryForAllFiles(directory: File, allFiles: MutableList<FileItem>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    scanDirectoryForAllFiles(file, allFiles)
                } else {
                    allFiles.add(FileItem(file))
                }
            }
        } catch (e: Exception) {
            // Handle permission errors
        }
    }

    private fun scanDirectoryForImages(directory: File, images: MutableList<FileItem>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    scanDirectoryForImages(file, images)
                } else {
                    val fileItem = FileItem(file)
                    if (fileItem.isImage) {
                        images.add(fileItem)
                    }
                }
            }
        } catch (e: Exception) {
            // Handle permission errors
        }
    }

    private fun findFilesByExtension(extensions: List<String>): List<FileItem> {
        val files = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()

        scanDirectoryForExtensions(rootDir, files, extensions)

        return files
    }

    private fun scanDirectoryForExtensions(directory: File, files: MutableList<FileItem>, extensions: List<String>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    scanDirectoryForExtensions(file, files, extensions)
                } else if (extensions.any { ext -> file.extension.equals(ext, ignoreCase = true) }) {
                    files.add(FileItem(file))
                }
            }
        } catch (e: Exception) {
            // Handle permission errors
        }
    }

    private fun findFilesInDirectory(directoryName: String): List<FileItem> {
        val files = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()
        val targetDir = File(rootDir, directoryName)

        if (targetDir.exists() && targetDir.isDirectory) {
            scanDirectoryForAllFiles(targetDir, files)
        }

        return files
    }

    private fun findScreenshotFiles(): List<FileItem> {
        val screenshots = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()

        // Common screenshot directories
        val screenshotDirs = listOf("Screenshots", "Pictures/Screenshots", "DCIM/Screenshots")

        screenshotDirs.forEach { dirPath ->
            val dir = File(rootDir, dirPath)
            if (dir.exists() && dir.isDirectory) {
                scanDirectoryForAllFiles(dir, screenshots)
            }
        }

        return screenshots
    }

    private fun findTempFiles(): List<FileItem> {
        val tempFiles = mutableListOf<FileItem>()
        val rootDir = Environment.getExternalStorageDirectory()

        scanDirectoryForTempFiles(rootDir, tempFiles)

        return tempFiles
    }

    private fun scanDirectoryForTempFiles(directory: File, tempFiles: MutableList<FileItem>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    if (file.name.equals("temp", ignoreCase = true) ||
                        file.name.equals("tmp", ignoreCase = true)) {
                        scanDirectoryForAllFiles(file, tempFiles)
                    } else {
                        scanDirectoryForTempFiles(file, tempFiles)
                    }
                } else if (file.name.startsWith("temp", ignoreCase = true) ||
                          file.name.startsWith("tmp", ignoreCase = true) ||
                          file.extension.equals("tmp", ignoreCase = true)) {
                    tempFiles.add(FileItem(file))
                }
            }
        } catch (e: Exception) {
            // Handle permission errors
        }
    }

    private fun calculateFileHash(file: File): String? {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            file.inputStream().use { input ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            null
        }
    }
}
