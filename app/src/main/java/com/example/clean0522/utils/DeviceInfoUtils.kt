package com.example.clean0522.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.provider.Settings
import android.text.format.Formatter
import java.io.BufferedReader
import java.io.FileReader
import com.example.clean0522.R

/**
 * Utility class for retrieving device information
 */
object DeviceInfoUtils {

    /**
     * Get Android device ID
     */
    @SuppressLint("HardwareIds")
    fun getAndroidDeviceId(context: Context): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get Bluetooth MAC address
     * Note: Returns "Unknown" on Android 6.0+ due to privacy restrictions
     */
    fun getBluetoothMacAddress(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                "Restricted (Android 6.0+)"
            } else {
                "Unknown"
            }
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get device board
     */
    fun getBoard(): String = Build.BOARD ?: "Unknown"

    /**
     * Get device brand
     */
    fun getBrand(): String = Build.BRAND ?: "Unknown"

    /**
     * Get build fingerprint
     */
    fun getBuildFingerprint(): String = Build.FINGERPRINT ?: "Unknown"

    /**
     * Get device name
     */
    fun getDevice(): String = Build.DEVICE ?: "Unknown"

    /**
     * Get device display name
     */
    fun getDeviceName(): String = Build.MODEL ?: "Unknown"

    /**
     * Get hardware information
     */
    fun getHardware(): String = Build.HARDWARE ?: "Unknown"

    /**
     * Get hardware serial
     * Note: Returns "Unknown" on Android 8.0+ due to privacy restrictions
     */
    @SuppressLint("HardwareIds")
    fun getHardwareSerial(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                "Restricted (Android 8.0+)"
            } else {
                @Suppress("DEPRECATION")
                Build.SERIAL ?: "Unknown"
            }
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get manufacturer
     */
    fun getManufacturer(): String = Build.MANUFACTURER ?: "Unknown"

    /**
     * Get device model
     */
    fun getModel(): String = Build.MODEL ?: "Unknown"

    /**
     * Check if USB debugging is enabled
     */
    fun isUsbDebuggingEnabled(context: Context): String {
        return try {
            val adbEnabled = Settings.Global.getInt(
                context.contentResolver,
                Settings.Global.ADB_ENABLED,
                0
            )
            if (adbEnabled == 1) "Enabled" else "Disabled"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get WiFi MAC address
     * Note: Returns "Unknown" on Android 6.0+ due to privacy restrictions
     */
    @SuppressLint("HardwareIds")
    fun getWifiMacAddress(context: Context): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                "Restricted (Android 6.0+)"
            } else {
                val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                @Suppress("DEPRECATION")
                wifiManager.connectionInfo?.macAddress ?: "Unknown"
            }
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get Android version
     */
    fun getAndroidVersion(): String = "Android ${Build.VERSION.RELEASE}"

    /**
     * Get API level
     */
    fun getApiLevel(): String = Build.VERSION.SDK_INT.toString()

    /**
     * Get total storage
     */
    fun getTotalStorage(context: Context): String {
        return try {
            val stat = StatFs(Environment.getExternalStorageDirectory().path)
            val totalBytes = stat.totalBytes
            Formatter.formatFileSize(context, totalBytes)
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get available storage
     */
    fun getAvailableStorage(context: Context): String {
        return try {
            val stat = StatFs(Environment.getExternalStorageDirectory().path)
            val availableBytes = stat.availableBytes
            Formatter.formatFileSize(context, availableBytes)
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get total RAM
     */
    fun getTotalRam(): String {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()
            
            if (line != null) {
                val memInfo = line.split("\\s+".toRegex())
                if (memInfo.size >= 2) {
                    val totalKb = memInfo[1].toLong()
                    val totalMb = totalKb / 1024
                    val totalGb = totalMb / 1024
                    return if (totalGb > 0) {
                        "${totalGb} GB"
                    } else {
                        "${totalMb} MB"
                    }
                }
            }
            "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get processor information
     */
    fun getProcessor(): String {
        return try {
            val abi = Build.SUPPORTED_ABIS?.firstOrNull() ?: Build.CPU_ABI
            abi ?: "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    /**
     * Get all device info items
     */
    fun getAllDeviceInfoItems(context: Context): List<DeviceInfoData> {
        return listOf(
            DeviceInfoData(
                title = context.getString(R.string.android_device_id),
                value = getAndroidDeviceId(context),
                icon = R.mipmap.de_id
            ),
            DeviceInfoData(
                title = context.getString(R.string.bluetooth_mac_address),
                value = getBluetoothMacAddress(),
                icon = R.mipmap.de_blue
            ),
            DeviceInfoData(
                title = context.getString(R.string.board),
                value = getBoard(),
                icon = R.mipmap.de_board
            ),
            DeviceInfoData(
                title = context.getString(R.string.brand),
                value = getBrand(),
                icon = R.mipmap.de_brand
            ),
            DeviceInfoData(
                title = context.getString(R.string.build_fingerprint),
                value = getBuildFingerprint(),
                icon = R.mipmap.de_finger
            ),
            DeviceInfoData(
                title = context.getString(R.string.device),
                value = getDevice(),
                icon = R.mipmap.de_device
            ),
            DeviceInfoData(
                title = context.getString(R.string.device_name),
                value = getDeviceName(),
                icon = R.mipmap.de_name
            ),
            DeviceInfoData(
                title = context.getString(R.string.hardware),
                value = getHardware(),
                icon = R.mipmap.de_hardware
            ),
            DeviceInfoData(
                title = context.getString(R.string.hardware_serial),
                value = getHardwareSerial(),
                icon = R.mipmap.de_serial
            ),
            DeviceInfoData(
                title = context.getString(R.string.manufacturer),
                value = getManufacturer(),
                icon = R.mipmap.de_manu
            ),
            DeviceInfoData(
                title = context.getString(R.string.model),
                value = getModel(),
                icon = R.mipmap.de_model
            ),
            DeviceInfoData(
                title = context.getString(R.string.usb_debugging),
                value = isUsbDebuggingEnabled(context),
                icon = R.mipmap.de_usb
            ),
            DeviceInfoData(
                title = context.getString(R.string.wifi_mac_address),
                value = getWifiMacAddress(context),
                icon = R.mipmap.de_wifi
            )
        )
    }
}
