package com.example.clean0522.utils

import android.app.ActivityManager
import android.content.Context
import com.example.clean0522.CleanApp
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import java.io.BufferedReader
import java.io.FileReader

/**
 * Utility class for RAM usage monitoring
 */
object RamUsageUtils {

    /**
     * Data class for RAM usage information
     */
    data class RamUsageInfo(
        val usedMemoryBytes: Long,
        val totalMemoryBytes: Long,
        val availableMemoryBytes: Long,
        val usagePercentage: Float,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        val usedMemoryFormatted: String
            get() = formatBytes(usedMemoryBytes)

        val totalMemoryFormatted: String
            get() = formatBytes(totalMemoryBytes)

        val availableMemoryFormatted: String
            get() = formatBytes(availableMemoryBytes)
    }

    /**
     * Get current RAM usage information
     */
    fun getCurrentRamUsage(context: Context): RamUsageInfo {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val totalMemory = getTotalMemoryFromProcMeminfo()
            val availableMemory = memoryInfo.availMem
            val usedMemory = totalMemory - availableMemory
            val usagePercentage = if (totalMemory > 0) {
                (usedMemory.toFloat() / totalMemory.toFloat()) * 100f
            } else {
                0f
            }

            RamUsageInfo(
                usedMemoryBytes = usedMemory,
                totalMemoryBytes = totalMemory,
                availableMemoryBytes = availableMemory,
                usagePercentage = usagePercentage
            )
        } catch (e: Exception) {
            // Fallback to default values
            RamUsageInfo(
                usedMemoryBytes = 0L,
                totalMemoryBytes = 1L,
                availableMemoryBytes = 1L,
                usagePercentage = 0f
            )
        }
    }

    /**
     * Get real-time RAM usage flow that updates every second
     */
    fun getRamUsageFlow(context: Context): Flow<RamUsageInfo> = flow {
        while (true) {
            emit(getCurrentRamUsage(context))
            delay(1000) // Update every 1 second
        }
    }

    /**
     * Get total memory from /proc/meminfo for more accurate reading
     */
    private fun getTotalMemoryFromProcMeminfo(): Long {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()

            if (line != null) {
                val memInfo = line.split("\\s+".toRegex())
                if (memInfo.size >= 2) {
                    val totalKb = memInfo[1].toLong()
                    return totalKb * 1024 // Convert KB to bytes
                }
            }
            // Fallback to Runtime if /proc/meminfo fails
            Runtime.getRuntime().maxMemory()
        } catch (e: Exception) {
            // Fallback to Runtime
            Runtime.getRuntime().maxMemory()
        }
    }

    /**
     * Format bytes to human readable format
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }

    /**
     * Data class for chart data point
     */
    data class ChartDataPoint(
        val usedMemoryBytes: Long,
        val timestamp: Long
    )

    /**
     * Manage chart data points (keep only last 10 points)
     */
    class ChartDataManager {
        private val maxDataPoints = 10
        private val dataPoints = mutableListOf<ChartDataPoint>()
        private var isInitialized = false

        fun addDataPoint(ramUsageInfo: RamUsageInfo) {
            if (!isInitialized) {
                initializeDataPoints(getCurrentRamUsage(CleanApp.appContext).usedMemoryBytes)
                isInitialized = true
            }

            val newPoint = ChartDataPoint(
                usedMemoryBytes = ramUsageInfo.usedMemoryBytes,
                timestamp = ramUsageInfo.timestamp
            )

            dataPoints.add(newPoint)

            // Keep only the last 10 points
            if (dataPoints.size > maxDataPoints) {
                dataPoints.removeAt(0)
            }
        }

        /**
         * Initialize 10 data points with current RAM value
         */
        private fun initializeDataPoints(currentRamBytes: Long) {
            val currentTime = System.currentTimeMillis()
            for (i in 0 until maxDataPoints) {
                dataPoints.add(
                    ChartDataPoint(
                        usedMemoryBytes = currentRamBytes,
                        timestamp = currentTime - (maxDataPoints - 1 - i) * 1000L
                    )
                )
            }
        }

        fun getDataPoints(): List<ChartDataPoint> = dataPoints.toList()

        fun getMinMaxMemoryBytes(): Pair<Long, Long> {
            if (dataPoints.isEmpty()) return Pair(0L, 1024L * 1024L * 1024L) // Default 0 to 1GB

            val min = dataPoints.minOf { it.usedMemoryBytes }
            val max = dataPoints.maxOf { it.usedMemoryBytes }

            // If we only have one data point or all points are the same
            if (dataPoints.size == 1 || min == max) {
                val center = min
                val range = 200L * 1024L * 1024L // Show a 200MB range around the single value for better visibility
                return Pair(
                    (center - range / 2).coerceAtLeast(0L),
                    center + range / 2
                )
            }

            // Add padding to make changes more visible - use smaller padding for more sensitivity
            val padding = ((max - min) * 0.15f).toLong() // Slightly more padding for better visualization
            val adjustedMin = (min - padding).coerceAtLeast(0L)
            val adjustedMax = max + padding

            // Ensure minimum range for visibility - smaller range for more sensitivity
            val minRange = 100L * 1024L * 1024L // 100MB minimum range for good sensitivity
            return if (adjustedMax - adjustedMin < minRange) {
                val center = (adjustedMin + adjustedMax) / 2L
                val halfRange = minRange / 2L
                Pair(
                    (center - halfRange).coerceAtLeast(0L),
                    center + halfRange
                )
            } else {
                Pair(adjustedMin, adjustedMax)
            }
        }


    }
}
