package com.example.clean0522.utils

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * Utility class for app process monitoring and management
 */
object AppProcessUtils {

    /**
     * Data class representing a running app
     */
    data class RunningAppInfo(
        val packageName: String,
        val appName: String,
        val icon: Drawable?,
        val isSystemApp: Boolean,
        val isStopped: Boolean = false
    )

    /**
     * Data class representing app process summary
     */
    data class AppProcessSummary(
        val runningAppsCount: Int,
        val ramUsagePercentage: Float,
        val runningApps: List<RunningAppInfo>
    )

    /**
     * Get current running user apps (non-system apps)
     */
    suspend fun getRunningUserApps(context: Context): List<RunningAppInfo> = withContext(Dispatchers.IO) {
        try {
            val packageManager = context.packageManager
            val intent = Intent(Intent.ACTION_MAIN, null).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }

            val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
            val runningApps = mutableListOf<RunningAppInfo>()

            for (resolveInfo in resolveInfoList) {
                val packageName = resolveInfo.activityInfo.packageName
                try {
                    val applicationInfo = packageManager.getApplicationInfo(packageName, 0)

                    // Filter for user apps that are not stopped
                    val isUserApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) == 0
                    val isNotStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) == 0

                    if (isUserApp && isNotStopped) {
                        val appName = packageManager.getApplicationLabel(applicationInfo).toString()
                        val icon = try {
                            packageManager.getApplicationIcon(applicationInfo)
                        } catch (e: Exception) {
                            null
                        }

                        runningApps.add(
                            RunningAppInfo(
                                packageName = packageName,
                                appName = appName,
                                icon = icon,
                                isSystemApp = false,
                                isStopped = false
                            )
                        )
                    }
                } catch (e: Exception) {
                    // Skip apps that can't be queried
                    logE("Error getting app info for $packageName", e)
                }
            }

            // Sort by app name
            runningApps.sortedBy { it.appName }
        } catch (e: Exception) {
            logE("Error getting running user apps", e)
            emptyList()
        }
    }

    /**
     * Get app process summary with RAM usage
     */
    suspend fun getAppProcessSummary(context: Context): AppProcessSummary = withContext(Dispatchers.IO) {
        try {
            val runningApps = getRunningUserApps(context)
            val ramUsage = RamUsageUtils.getCurrentRamUsage(context)

            AppProcessSummary(
                runningAppsCount = runningApps.size,
                ramUsagePercentage = ramUsage.usagePercentage,
                runningApps = runningApps
            )
        } catch (e: Exception) {
            logE("Error getting app process summary", e)
            AppProcessSummary(
                runningAppsCount = 0,
                ramUsagePercentage = 0f,
                runningApps = emptyList()
            )
        }
    }

    /**
     * Get real-time app process summary flow that updates every 3 seconds
     */
    fun getAppProcessSummaryFlow(context: Context): Flow<AppProcessSummary> = flow {
        while (true) {
            emit(getAppProcessSummary(context))
            delay(3000) // Update every 3 seconds
        }
    }

    /**
     * Stop an app by opening its settings page
     * Note: We can't directly stop apps due to Android security restrictions
     */
    fun openAppSettings(context: Context, packageName: String): Boolean {
        return try {
            val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = android.net.Uri.parse("package:$packageName")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            logE("Error opening app settings for $packageName", e)
            false
        }
    }

    /**
     * Check if an app is currently stopped or not running
     */
    fun isAppStopped(context: Context, packageName: String): Boolean {
        return try {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)

            // Check if app is marked as stopped
            val isFlagStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) != 0

            // Also check if app is in the current running apps list
            val isInRunningList = isAppInRunningList(context, packageName)

            // App is considered stopped if it has the stopped flag OR is not in running list
            val isStopped = isFlagStopped || !isInRunningList

            logD("AppProcessUtils", "App $packageName - FlagStopped: $isFlagStopped, InRunningList: $isInRunningList, IsStopped: $isStopped")

            isStopped
        } catch (e: Exception) {
            logE("Error checking if app is stopped: $packageName", e)
            true // Assume stopped if we can't check
        }
    }

    /**
     * Check if an app is in the current running apps list
     */
    private fun isAppInRunningList(context: Context, packageName: String): Boolean {
        return try {
            val packageManager = context.packageManager
            val intent = Intent(Intent.ACTION_MAIN, null).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }

            val resolveInfoList = packageManager.queryIntentActivities(intent, 0)

            for (resolveInfo in resolveInfoList) {
                if (resolveInfo.activityInfo.packageName == packageName) {
                    try {
                        val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
                        val isUserApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) == 0
                        val isNotStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) == 0

                        return isUserApp && isNotStopped
                    } catch (e: Exception) {
                        return false
                    }
                }
            }
            false
        } catch (e: Exception) {
            logE("Error checking if app is in running list: $packageName", e)
            false
        }
    }

    /**
     * Get total number of installed user apps
     */
    fun getTotalUserAppsCount(context: Context): Int {
        return try {
            val packageManager = context.packageManager
            val intent = Intent(Intent.ACTION_MAIN, null).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }

            val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
            var userAppsCount = 0

            for (resolveInfo in resolveInfoList) {
                try {
                    val applicationInfo = packageManager.getApplicationInfo(
                        resolveInfo.activityInfo.packageName, 0
                    )
                    if ((applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) == 0) {
                        userAppsCount++
                    }
                } catch (e: Exception) {
                    // Skip apps that can't be queried
                }
            }

            userAppsCount
        } catch (e: Exception) {
            logE("Error getting total user apps count", e)
            0
        }
    }
}
