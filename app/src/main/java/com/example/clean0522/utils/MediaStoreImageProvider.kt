package com.example.clean0522.utils

import android.content.Context
import android.net.Uri
import android.provider.MediaStore

import com.example.clean0522.domain.model.FileItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Optimized image provider using MediaStore for fast image scanning
 */
object MediaStoreImageProvider {

    private const val TAG = "MediaStoreImageProvider"

    // MediaStore projection for image queries
    private val IMAGE_PROJECTION = arrayOf(
        MediaStore.Images.Media._ID,
        MediaStore.Images.Media.DATA,
        MediaStore.Images.Media.DISPLAY_NAME,
        MediaStore.Images.Media.SIZE,
        MediaStore.Images.Media.DATE_MODIFIED,
        MediaStore.Images.Media.WIDTH,
        MediaStore.Images.Media.HEIGHT,
        MediaStore.Images.Media.MIME_TYPE
    )

    // Selection criteria for images
    private const val IMAGE_SELECTION = "${MediaStore.Images.Media.SIZE} > ? AND ${MediaStore.Images.Media.MIME_TYPE} LIKE 'image/%'"
    private val IMAGE_SELECTION_ARGS = arrayOf(EnhancedSimilarImageDetector.MIN_FILE_SIZE.toString())

    // Sort order for better performance
    private const val IMAGE_SORT_ORDER = "${MediaStore.Images.Media.DATE_MODIFIED} DESC"

    /**
     * Get all images using MediaStore (much faster than file system scanning)
     */
    suspend fun getAllImages(context: Context): List<ImageMetadata> = withContext(Dispatchers.IO) {
        val images = mutableListOf<ImageMetadata>()
        val contentResolver = context.contentResolver

        try {
            val cursor = contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                IMAGE_PROJECTION,
                IMAGE_SELECTION,
                IMAGE_SELECTION_ARGS,
                IMAGE_SORT_ORDER
            )

            cursor?.use { c ->
                val idColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
                val dataColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                val nameColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
                val sizeColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
                val modifiedColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED)
                val widthColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.WIDTH)
                val heightColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.HEIGHT)
                val mimeColumn = c.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE)

                while (c.moveToNext()) {
                    try {
                        val id = c.getLong(idColumn)
                        val path = c.getString(dataColumn)
                        val name = c.getString(nameColumn)
                        val size = c.getLong(sizeColumn)
                        val modified = c.getLong(modifiedColumn) * 1000 // Convert to milliseconds
                        val width = c.getInt(widthColumn)
                        val height = c.getInt(heightColumn)
                        val mimeType = c.getString(mimeColumn)

                        // Verify file still exists
                        val file = File(path)
                        if (file.exists() && file.canRead()) {
                            images.add(
                                ImageMetadata(
                                    id = id,
                                    path = path,
                                    name = name,
                                    size = size,
                                    lastModified = modified,
                                    width = width,
                                    height = height,
                                    mimeType = mimeType,
                                    uri = Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id.toString())
                                )
                            )
                        }
                    } catch (e: Exception) {
                        LogUtils.w(TAG, "Error processing image record", e)
                    }
                }
            }

            LogUtils.d(TAG, "Found ${images.size} images using MediaStore")

        } catch (e: Exception) {
            LogUtils.e(TAG, "Error querying MediaStore for images", e)
        }

        images
    }

    /**
     * Convert ImageMetadata to FileItem for compatibility
     */
    fun ImageMetadata.toFileItem(): FileItem {
        return FileItem(File(this.path))
    }

    /**
     * Get thumbnail URI for image
     */
    fun getThumbnailUri(context: Context, imageId: Long): Uri? {
        return try {
            Uri.withAppendedPath(MediaStore.Images.Thumbnails.EXTERNAL_CONTENT_URI, imageId.toString())
        } catch (e: Exception) {
            LogUtils.w(TAG, "Error getting thumbnail URI for image: $imageId", e)
            null
        }
    }

    /**
     * Check if image has good quality for similarity detection
     */
    fun ImageMetadata.isGoodQuality(): Boolean {
        return width >= 100 && height >= 100 && // Minimum resolution
               size >= EnhancedSimilarImageDetector.MIN_FILE_SIZE && // Minimum file size
               width * height <= 50_000_000 // Maximum pixels to avoid huge images
    }

    /**
     * Filter images for similarity detection
     */
    fun List<ImageMetadata>.filterForSimilarityDetection(): List<ImageMetadata> {
        return this.filter { it.isGoodQuality() }
            .distinctBy { it.path } // Remove duplicates
            .sortedByDescending { it.size } // Process larger images first
    }
}

/**
 * Enhanced image metadata from MediaStore
 */
data class ImageMetadata(
    val id: Long,
    val path: String,
    val name: String,
    val size: Long,
    val lastModified: Long,
    val width: Int,
    val height: Int,
    val mimeType: String,
    val uri: Uri
) {
    /**
     * Calculate aspect ratio
     */
    val aspectRatio: Double
        get() = if (height > 0) width.toDouble() / height else 1.0

    /**
     * Get file extension
     */
    val extension: String
        get() = File(path).extension.lowercase()

    /**
     * Check if image is likely a screenshot
     */
    val isLikelyScreenshot: Boolean
        get() = path.contains("screenshot", ignoreCase = true) ||
                name.contains("screenshot", ignoreCase = true) ||
                (width == 1080 && height == 1920) || // Common phone resolution
                (width == 1440 && height == 2560)   // Common phone resolution
}
