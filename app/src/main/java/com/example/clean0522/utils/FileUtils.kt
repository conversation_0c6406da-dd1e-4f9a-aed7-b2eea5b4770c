package com.example.clean0522.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.webkit.MimeTypeMap
import androidx.core.content.FileProvider
import com.example.clean0522.domain.model.FileItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.text.DecimalFormat

/**
 * File utility class
 */
object FileUtils {
    /**
     * Format file size
     */
    fun formatFileSize(size: Long): String {
        if (size <= 0) return "0MB"
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()
        return DecimalFormat("#,##0.#").format(size / Math.pow(1024.0, digitGroups.toDouble())) + units[digitGroups]
    }

    /**
     * Get all files in the specified directory
     */
    fun getFilesInDirectory(directory: File): List<FileItem> {
        val files = directory.listFiles()
        if (files == null || files.isEmpty()) {
            return emptyList()
        }

        return files.map { FileItem(it) }
            .sortedWith(compareBy({ !it.isDirectory }, { it.name.lowercase() }))
    }

    /**
     * Asynchronously get all files in the specified directory
     */
    suspend fun getFilesInDirectoryAsync(directory: File): List<FileItem> {
        return withContext(Dispatchers.IO) {
            getFilesInDirectory(directory)
        }
    }

    /**
     * Get the MIME type of a file
     */
    fun getMimeType(file: File): String {
        val extension = MimeTypeMap.getFileExtensionFromUrl(file.absolutePath)
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "*/*"
    }

    /**
     * Open a file
     */
    fun openFile(context: Context, file: File) {
        try {
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )
            val intent = Intent(Intent.ACTION_VIEW)
            intent.setDataAndType(uri, getMimeType(file))
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            context.startActivity(intent)
        } catch (e: IllegalArgumentException) {
            try {
                val uri = Uri.fromFile(file)
                val intent = Intent(Intent.ACTION_VIEW)
                intent.setDataAndType(uri, getMimeType(file))
                context.startActivity(intent)
            } catch (e2: Exception) {
                e2.printStackTrace()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Share a file
     */
    fun shareFile(context: Context, file: File) {
        try {
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file
            )
            val intent = Intent(Intent.ACTION_SEND)
            intent.type = getMimeType(file)
            intent.putExtra(Intent.EXTRA_STREAM, uri)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            context.startActivity(Intent.createChooser(intent, "分享文件"))
        } catch (e: IllegalArgumentException) {
            // If FileProvider fails, try with direct file URI for older Android versions
            try {
                val uri = Uri.fromFile(file)
                val intent = Intent(Intent.ACTION_SEND)
                intent.type = getMimeType(file)
                intent.putExtra(Intent.EXTRA_STREAM, uri)
                context.startActivity(Intent.createChooser(intent, "分享文件"))
            } catch (e2: Exception) {
                e2.printStackTrace()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * deleteFile
     */
    fun deleteFile(file: File): Boolean {
        if (file.isDirectory) {
            val children = file.listFiles()
            if (children != null) {
                for (child in children) {
                    deleteFile(child)
                }
            }
        }
        return file.delete()
    }

    /**
     * renameFile
     */
    fun renameFile(file: File, newName: String): Boolean {
        val newFile = File(file.parent, newName)
        return file.renameTo(newFile)
    }

    /**
     * getPathSegments
     */
    fun getPathSegments(path: String): List<String> {
        return path.split("/").filter { it.isNotEmpty() }
    }

    /**
     * Build path to the specified segment
     */
    fun buildPathToSegment(segments: List<String>, segmentIndex: Int): String {
        return "/" + segments.subList(0, segmentIndex + 1).joinToString("/")
    }
}