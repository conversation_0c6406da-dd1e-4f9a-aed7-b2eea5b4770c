package com.example.clean0522.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory

import com.example.clean0522.domain.model.FileGroup
import com.example.clean0522.utils.MediaStoreImageProvider.filterForSimilarityDetection
import com.example.clean0522.utils.MediaStoreImageProvider.toFileItem
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 * Enhanced similar image detector with improved accuracy and performance
 */
object EnhancedSimilarImageDetector {

    private const val TAG = "EnhancedSimilarImageDetector"

    // Stricter thresholds for better accuracy
    private const val HIGH_SIMILARITY_THRESHOLD = 95.0 // Very similar
    private const val MEDIUM_SIMILARITY_THRESHOLD = 90.0 // Quite similar
    private const val LOW_SIMILARITY_THRESHOLD = 85.0 // Possibly similar

    // Hamming distance thresholds for different hash types
    private const val AHASH_THRESHOLD = 5 // Average hash (stricter)
    private const val DHASH_THRESHOLD = 6 // Difference hash
    private const val PHASH_THRESHOLD = 8 // Perceptual hash (more lenient)

    const val MIN_FILE_SIZE = 5120L

    // Cache for computed hashes
    private val hashCache = ConcurrentHashMap<String, EnhancedImageHash>()

    /**
     * Find similar photos with enhanced accuracy
     */
    suspend fun findSimilarPhotos(context: Context): List<FileGroup> = withContext(Dispatchers.IO) {
        return@withContext PerformanceMonitor.measureSuspend("Enhanced Similar Photo Detection") {
            try {
                logD("Starting enhanced similar photo detection...")
                logMemory("Start Detection")

                // 1. Get images using MediaStore (much faster)
                val allImages = PerformanceMonitor.measureSuspend("MediaStore Image Scan") {
                    MediaStoreImageProvider.getAllImages(context)
                        .filterForSimilarityDetection()
                        .take(500) // Limit to 500 images for performance
                }

                logD("Found ${allImages.size} quality images for analysis")

                if (allImages.size < 2) {
                    logD("Not enough images for similarity detection")
                    return@measureSuspend emptyList()
                }

                // 2. Calculate hashes with parallel processing
                val imageHashes = PerformanceMonitor.measureSuspend("Hash Calculation") {
                    calculateHashesParallel(allImages)
                }
                logD("Calculated hashes for ${imageHashes.size} images")
                logMemory("After Hash Calculation")

                // 3. Group similar images with enhanced algorithm
                val similarGroups = PerformanceMonitor.measureSuspend("Similarity Grouping") {
                    groupSimilarImagesEnhanced(imageHashes)
                }
                logD("Found ${similarGroups.size} groups of similar images")

                // 4. Convert to FileGroup format
                val result = similarGroups.mapIndexed { index, group ->
                    val fileItems = group.map { it.toFileItem() }
                    FileGroup.fromFiles("Group ${index + 1}", fileItems)
                }

                logMemory("End Detection")
                result

            } catch (e: Exception) {
                logE("Error in enhanced similar photo detection", e)
                emptyList()
            }
        }
    }

    /**
     * Calculate hashes for images in parallel for better performance
     */
    private suspend fun calculateHashesParallel(images: List<ImageMetadata>): List<EnhancedImageHash> = withContext(Dispatchers.IO) {
        val semaphore = Semaphore(4) // Limit concurrent operations
        val results = mutableListOf<EnhancedImageHash>()

        // Process in batches to avoid memory issues
        images.chunked(20).forEach { batch ->
            val batchResults = batch.map { imageMetadata ->
                async {
                    semaphore.withPermit {
                        calculateImageHashEnhanced(imageMetadata)
                    }
                }
            }.awaitAll().filterNotNull()

            results.addAll(batchResults)

            // Small delay to prevent overwhelming the system
            delay(10)
        }

        results
    }

    /**
     * Calculate enhanced hash for a single image
     */
    private suspend fun calculateImageHashEnhanced(imageMetadata: ImageMetadata): EnhancedImageHash? = withContext(Dispatchers.IO) {
        try {
            // Check cache first
            val cachedHash = hashCache[imageMetadata.path]
            if (cachedHash != null) {
                return@withContext cachedHash
            }

            // Load image with optimized settings
            val bitmap = loadImageOptimized(File(imageMetadata.path))
            if (bitmap == null) {
                logW("Failed to load image: ${imageMetadata.path}")
                return@withContext null
            }

            // Calculate all hash types
            val averageHash = ImageHashUtils.calculateAverageHash(bitmap)
            val differenceHash = ImageHashUtils.calculateDifferenceHash(bitmap)
            val perceptualHash = ImageHashUtils.calculatePerceptualHash(bitmap)

            // Calculate additional features for better accuracy
            val colorHistogram = calculateColorHistogram(bitmap)
            val edgeHash = calculateEdgeHash(bitmap)

            bitmap.recycle()

            val enhancedHash = EnhancedImageHash(
                metadata = imageMetadata,
                averageHash = averageHash,
                differenceHash = differenceHash,
                perceptualHash = perceptualHash,
                colorHistogram = colorHistogram,
                edgeHash = edgeHash
            )

            // Cache the result
            hashCache[imageMetadata.path] = enhancedHash

            enhancedHash

        } catch (e: Exception) {
            logW("Error calculating hash for: ${imageMetadata.path}", e)
            null
        }
    }

    /**
     * Load image with memory optimization
     */
    private fun loadImageOptimized(file: File): Bitmap? {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(file.absolutePath, options)

            // Calculate optimal sample size
            val targetSize = 256 // Smaller size for faster processing
            options.inSampleSize = calculateInSampleSize(options, targetSize, targetSize)
            options.inJustDecodeBounds = false
            options.inPreferredConfig = Bitmap.Config.RGB_565

            BitmapFactory.decodeFile(file.absolutePath, options)
        } catch (e: Exception) {
            logW("Error loading image: ${file.path}", e)
            null
        }
    }

    /**
     * Calculate color histogram for additional similarity check
     */
    private fun calculateColorHistogram(bitmap: Bitmap): IntArray {
        val histogram = IntArray(64) // 4x4x4 RGB histogram
        val width = bitmap.width
        val height = bitmap.height

        for (x in 0 until width step 4) {
            for (y in 0 until height step 4) {
                val pixel = bitmap.getPixel(x, y)
                val r = ((pixel shr 16) and 0xFF) / 64
                val g = ((pixel shr 8) and 0xFF) / 64
                val b = (pixel and 0xFF) / 64
                val index = r * 16 + g * 4 + b
                histogram[index]++
            }
        }

        return histogram
    }

    /**
     * Calculate edge-based hash for structural similarity
     */
    private fun calculateEdgeHash(bitmap: Bitmap): Long {
        val resized = Bitmap.createScaledBitmap(bitmap, 8, 8, false)
        val pixels = IntArray(64)
        resized.getPixels(pixels, 0, 8, 0, 0, 8, 8)

        var hash = 0L
        for (i in 0 until 7) {
            for (j in 0 until 8) {
                val current = toGrayscale(pixels[i * 8 + j])
                val next = toGrayscale(pixels[(i + 1) * 8 + j])
                hash = hash shl 1
                if (current < next) {
                    hash = hash or 1
                }
            }
        }

        resized.recycle()
        return hash
    }

    /**
     * Group similar images with enhanced algorithm
     */
    private fun groupSimilarImagesEnhanced(imageHashes: List<EnhancedImageHash>): List<List<ImageMetadata>> {
        val groups = mutableListOf<MutableList<EnhancedImageHash>>()
        val processed = mutableSetOf<String>()

        for (i in imageHashes.indices) {
            val currentHash = imageHashes[i]
            if (processed.contains(currentHash.metadata.path)) continue

            val similarImages = mutableListOf<EnhancedImageHash>()
            similarImages.add(currentHash)
            processed.add(currentHash.metadata.path)

            // Find all similar images
            for (j in i + 1 until imageHashes.size) {
                val otherHash = imageHashes[j]
                if (processed.contains(otherHash.metadata.path)) continue

                if (areImagesSimilarEnhanced(currentHash, otherHash)) {
                    similarImages.add(otherHash)
                    processed.add(otherHash.metadata.path)

                    logD("Found similar images: ${currentHash.metadata.name} <-> ${otherHash.metadata.name}")
                }
            }

            // Only add groups with more than one image
            if (similarImages.size > 1) {
                groups.add(similarImages)
            }
        }

        return groups.map { hashGroup ->
            hashGroup.map { it.metadata }
        }
    }

    /**
     * Enhanced similarity check with multiple criteria
     */
    private fun areImagesSimilarEnhanced(hash1: EnhancedImageHash, hash2: EnhancedImageHash): Boolean {
        // 1. Basic metadata checks
        if (!areMetadataCompatible(hash1.metadata, hash2.metadata)) {
            return false
        }

        // 2. Hash-based similarity checks with stricter thresholds
        val aHashDistance = ImageHashUtils.hammingDistance(hash1.averageHash, hash2.averageHash)
        val dHashDistance = ImageHashUtils.hammingDistance(hash1.differenceHash, hash2.differenceHash)
        val pHashDistance = ImageHashUtils.hammingDistance(hash1.perceptualHash, hash2.perceptualHash)

        // At least two algorithms must agree
        var agreementCount = 0
        if (aHashDistance <= AHASH_THRESHOLD) agreementCount++
        if (dHashDistance <= DHASH_THRESHOLD) agreementCount++
        if (pHashDistance <= PHASH_THRESHOLD) agreementCount++

        if (agreementCount < 2) {
            return false
        }

        // 3. Color histogram similarity
        val colorSimilarity = calculateHistogramSimilarity(hash1.colorHistogram, hash2.colorHistogram)
        if (colorSimilarity < 0.7) {
            return false
        }

        // 4. Edge structure similarity
        val edgeDistance = ImageHashUtils.hammingDistance(hash1.edgeHash, hash2.edgeHash)
        if (edgeDistance > 15) {
            return false
        }

        return true
    }

    /**
     * Check if metadata suggests images could be similar
     */
    private fun areMetadataCompatible(meta1: ImageMetadata, meta2: ImageMetadata): Boolean {
        // Skip if same file
        if (meta1.path == meta2.path) return false

        // Check aspect ratio similarity
        val aspectRatioDiff = abs(meta1.aspectRatio - meta2.aspectRatio)
        if (aspectRatioDiff > 0.3) return false

        // Check size similarity (within 50% difference)
        val sizeDiff = abs(meta1.size - meta2.size).toDouble() / maxOf(meta1.size, meta2.size)
        if (sizeDiff > 0.5) return false

        return true
    }

    /**
     * Calculate histogram similarity using correlation
     */
    private fun calculateHistogramSimilarity(hist1: IntArray, hist2: IntArray): Double {
        var correlation = 0.0
        var sum1 = 0
        var sum2 = 0

        for (i in hist1.indices) {
            correlation += hist1[i] * hist2[i]
            sum1 += hist1[i] * hist1[i]
            sum2 += hist2[i] * hist2[i]
        }

        return if (sum1 > 0 && sum2 > 0) {
            correlation / kotlin.math.sqrt(sum1.toDouble() * sum2.toDouble())
        } else 0.0
    }

    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }

    private fun toGrayscale(pixel: Int): Int {
        val r = (pixel shr 16) and 0xFF
        val g = (pixel shr 8) and 0xFF
        val b = pixel and 0xFF
        return (0.299 * r + 0.587 * g + 0.114 * b).toInt()
    }

    /**
     * Clear cache to free memory
     */
    fun clearCache() {
        hashCache.clear()
        logD("Enhanced hash cache cleared")
    }
}

/**
 * Enhanced image hash with additional features
 */
data class EnhancedImageHash(
    val metadata: ImageMetadata,
    val averageHash: Long,
    val differenceHash: Long,
    val perceptualHash: Long,
    val colorHistogram: IntArray,
    val edgeHash: Long
) {
    fun toFileItem() = metadata.toFileItem()
}
