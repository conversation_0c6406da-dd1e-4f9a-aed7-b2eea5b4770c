package com.example.clean0522.utils

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import com.example.clean0522.R

/**
 * Utility class for battery information monitoring
 */
object BatteryInfoUtils {

    /**
     * Data class for battery information
     */
    data class BatteryInfo(
        val level: Int,
        val health: Int,
        val status: Int,
        val plugged: Int,
        val voltage: Int,
        val temperature: Int,
        val current: Int,
        val capacity: Int,
        val technology: String,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        /**
         * Get battery health string resource ID
         */
        fun getHealthStringRes(): Int {
            return when (health) {
                BatteryManager.BATTERY_HEALTH_GOOD -> com.example.clean0522.R.string.battery_health_good
                BatteryManager.BATTERY_HEALTH_OVERHEAT -> com.example.clean0522.R.string.battery_health_overheat
                BatteryManager.BATTERY_HEALTH_DEAD -> com.example.clean0522.R.string.battery_health_dead
                BatteryManager.BATTERY_HEALTH_OVER_VOLTAGE -> com.example.clean0522.R.string.battery_health_over_voltage
                BatteryManager.BATTERY_HEALTH_UNSPECIFIED_FAILURE -> com.example.clean0522.R.string.battery_health_unspecified_failure
                BatteryManager.BATTERY_HEALTH_COLD -> com.example.clean0522.R.string.battery_health_cold
                else -> com.example.clean0522.R.string.battery_health_unknown
            }
        }

        /**
         * Get battery status string resource ID
         */
        fun getStatusStringRes(): Int {
            return when (status) {
                BatteryManager.BATTERY_STATUS_CHARGING -> com.example.clean0522.R.string.battery_status_charging
                BatteryManager.BATTERY_STATUS_DISCHARGING -> com.example.clean0522.R.string.battery_status_discharging
                BatteryManager.BATTERY_STATUS_NOT_CHARGING -> com.example.clean0522.R.string.battery_status_not_charging
                BatteryManager.BATTERY_STATUS_FULL -> com.example.clean0522.R.string.battery_status_full
                else -> com.example.clean0522.R.string.battery_status_unknown
            }
        }

        /**
         * Get power source string resource ID
         */
        fun getSourceStringRes(): Int {
            return when (plugged) {
                BatteryManager.BATTERY_PLUGGED_AC -> com.example.clean0522.R.string.battery_source_ac
                BatteryManager.BATTERY_PLUGGED_USB -> com.example.clean0522.R.string.battery_source_usb
                BatteryManager.BATTERY_PLUGGED_WIRELESS -> com.example.clean0522.R.string.battery_source_wireless
                else -> com.example.clean0522.R.string.battery_source_battery
            }
        }

        /**
         * Get temperature in Celsius
         */
        val temperatureCelsius: Float
            get() = temperature / 10.0f

        /**
         * Get formatted current in mA
         */
        val currentMa: Int
            get() = current // Convert from microamps to milliamps

        /**
         * Get formatted capacity in mAh
         */
        val capacityMah: Int
            get() = capacity
    }

    /**
     * Get current battery information
     */
    fun getCurrentBatteryInfo(context: Context): BatteryInfo {
        val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))

        return if (batteryIntent != null) {
            val level = batteryIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
            val scale = batteryIntent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
            val health = batteryIntent.getIntExtra(BatteryManager.EXTRA_HEALTH, BatteryManager.BATTERY_HEALTH_UNKNOWN)
            val status = batteryIntent.getIntExtra(BatteryManager.EXTRA_STATUS, BatteryManager.BATTERY_STATUS_UNKNOWN)
            val plugged = batteryIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)
            val voltage = batteryIntent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0)
            val temperature = batteryIntent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0)
            val technology = batteryIntent.getStringExtra(BatteryManager.EXTRA_TECHNOLOGY) ?: "N/A"

            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            val current = getCurrentBatt(context)

            val capacity = try {
                val chargeCounter = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CHARGE_COUNTER)
                val currentLevel = if (scale > 0) (level * 100 / scale) else 100

                if (chargeCounter > 0 && currentLevel > 0) {
                    val currentChargeMah = chargeCounter / 1000
                    (currentChargeMah * 100) / currentLevel
                } else {
                    estimateBatteryCapacity()
                }
            } catch (e: Exception) {
                estimateBatteryCapacity()
            }

            BatteryInfo(
                level = if (scale > 0) (level * 100 / scale) else 0,
                health = health,
                status = status,
                plugged = plugged,
                voltage = voltage,
                temperature = temperature,
                current = current,
                capacity = capacity,
                technology = technology
            )
        } else {
            // Fallback battery info
            BatteryInfo(
                level = 0,
                health = BatteryManager.BATTERY_HEALTH_UNKNOWN,
                status = BatteryManager.BATTERY_STATUS_UNKNOWN,
                plugged = 0,
                voltage = 0,
                temperature = 0,
                current = 0,
                capacity = 0,
                technology = "N/A"
            )
        }
    }

    /**
     * Get real-time battery info flow that updates every 2 seconds
     */
    fun getBatteryInfoFlow(context: Context): Flow<BatteryInfo> = flow {
        while (true) {
            emit(getCurrentBatteryInfo(context))
            delay(2000)
        }
    }

    /**
     * Get all battery info items for display
     */
    fun getBatteryInfoItems(context: Context, batteryInfo: BatteryInfo): List<DeviceInfoData> {
        return listOf(
            DeviceInfoData(
                title = context.getString(R.string.battery_current),
                value = context.getString(R.string.battery_current_ma, batteryInfo.currentMa),
                icon = R.mipmap.ba_current
            ),
            DeviceInfoData(
                title = context.getString(R.string.battery_capacity),
                value = context.getString(R.string.battery_capacity_mah, batteryInfo.capacityMah),
                icon = R.mipmap.ba_capacity
            ),
            DeviceInfoData(
                title = context.getString(R.string.battery_source),
                value = context.getString(batteryInfo.getSourceStringRes()),
                icon = R.mipmap.ba_source
            ),
            DeviceInfoData(
                title = context.getString(R.string.battery_voltage),
                value = context.getString(R.string.battery_voltage_mv, batteryInfo.voltage),
                icon = R.mipmap.ba_voltage
            ),
            DeviceInfoData(
                title = context.getString(R.string.battery_temperature),
                value = context.getString(R.string.battery_temperature_celsius, batteryInfo.temperatureCelsius),
                icon = R.mipmap.ba_temp
            ),
            DeviceInfoData(
                title = context.getString(R.string.battery_technology),
                value = batteryInfo.technology,
                icon = R.mipmap.ba_tech
            )
        )
    }

    private fun getCurrentBatt(context: Context): Int {
        var current = 0
        try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            current = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            if (current >= 20000 || current <= -20000) {
                return current / 1000
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return current
    }

    /**
     * Estimate battery capacity based on device model and common battery capacities
     */
    private fun estimateBatteryCapacity(): Int {
        return try {
            // Try to read from system properties or device model
            val model = android.os.Build.MODEL.lowercase()
            val brand = android.os.Build.BRAND.lowercase()

            // Common battery capacities for different device types
            when {
                // Samsung devices
                brand.contains("samsung") -> {
                    when {
                        model.contains("galaxy s23") -> 3900
                        model.contains("galaxy s22") -> 3700
                        model.contains("galaxy s21") -> 4000
                        model.contains("galaxy note") -> 4300
                        model.contains("galaxy a") -> 4500
                        else -> 4000 // Default for Samsung
                    }
                }
                // Google Pixel devices
                brand.contains("google") -> {
                    when {
                        model.contains("pixel 7") -> 4355
                        model.contains("pixel 6") -> 4614
                        model.contains("pixel 5") -> 4080
                        model.contains("pixel 4") -> 2800
                        else -> 3500 // Default for Pixel
                    }
                }
                // iPhone (if running on iOS emulator or similar)
                brand.contains("apple") -> {
                    when {
                        model.contains("iphone 14") -> 3279
                        model.contains("iphone 13") -> 3240
                        model.contains("iphone 12") -> 2815
                        else -> 3000 // Default for iPhone
                    }
                }
                // Xiaomi devices
                brand.contains("xiaomi") || brand.contains("redmi") -> {
                    when {
                        model.contains("mi 11") -> 4600
                        model.contains("mi 10") -> 4780
                        model.contains("redmi note") -> 5000
                        else -> 4500 // Default for Xiaomi
                    }
                }
                // OnePlus devices
                brand.contains("oneplus") -> {
                    when {
                        model.contains("9") -> 4500
                        model.contains("8") -> 4300
                        else -> 4000 // Default for OnePlus
                    }
                }
                // Huawei devices
                brand.contains("huawei") -> {
                    when {
                        model.contains("p40") -> 3800
                        model.contains("mate") -> 4200
                        else -> 4000 // Default for Huawei
                    }
                }
                // Default for unknown devices
                else -> 3500
            }
        } catch (e: Exception) {
            3500 // Safe default capacity
        }
    }
}

data class DeviceInfoData(
    val title: String,
    val value: String,
    val icon: Int
)
