package com.example.clean0522.utils

import android.graphics.*
import kotlin.math.*

/**
 * Image similarity detection using perceptual hashing algorithms
 * Supports Average Hash, Difference Hash, and Perceptual Hash
 */
object ImageHashUtils {
    
    private const val HASH_SIZE = 8
    private const val DCT_SIZE = 32
    
    /**
     * Calculate Average Hash (aHash)
     * Fast and simple algorithm for basic similarity detection
     */
    fun calculateAverageHash(bitmap: Bitmap): Long {
        // 1. Resize to 8x8
        val resized = Bitmap.createScaledBitmap(bitmap, HASH_SIZE, HASH_SIZE, false)
        
        // 2. Convert to grayscale and calculate average
        val pixels = IntArray(HASH_SIZE * HASH_SIZE)
        resized.getPixels(pixels, 0, HASH_SIZE, 0, 0, HASH_SIZE, HASH_SIZE)
        
        var sum = 0L
        val grayPixels = IntArray(pixels.size)
        
        for (i in pixels.indices) {
            val gray = toGrayscale(pixels[i])
            grayPixels[i] = gray
            sum += gray
        }
        
        val average = sum / pixels.size
        
        // 3. Generate hash
        var hash = 0L
        for (i in grayPixels.indices) {
            hash = hash shl 1
            if (grayPixels[i] >= average) {
                hash = hash or 1
            }
        }
        
        resized.recycle()
        return hash
    }
    
    /**
     * Calculate Difference Hash (dHash)
     * More robust than aHash, detects structural changes better
     */
    fun calculateDifferenceHash(bitmap: Bitmap): Long {
        // 1. Resize to 9x8 (we need 9 columns to compare 8 differences)
        val resized = Bitmap.createScaledBitmap(bitmap, HASH_SIZE + 1, HASH_SIZE, false)
        
        // 2. Convert to grayscale
        val pixels = IntArray((HASH_SIZE + 1) * HASH_SIZE)
        resized.getPixels(pixels, 0, HASH_SIZE + 1, 0, 0, HASH_SIZE + 1, HASH_SIZE)
        
        val grayPixels = Array(HASH_SIZE) { IntArray(HASH_SIZE + 1) }
        for (y in 0 until HASH_SIZE) {
            for (x in 0 until HASH_SIZE + 1) {
                grayPixels[y][x] = toGrayscale(pixels[y * (HASH_SIZE + 1) + x])
            }
        }
        
        // 3. Calculate differences between adjacent pixels
        var hash = 0L
        for (y in 0 until HASH_SIZE) {
            for (x in 0 until HASH_SIZE) {
                hash = hash shl 1
                if (grayPixels[y][x] < grayPixels[y][x + 1]) {
                    hash = hash or 1
                }
            }
        }
        
        resized.recycle()
        return hash
    }
    
    /**
     * Calculate Perceptual Hash (pHash)
     * Most robust algorithm, uses DCT (Discrete Cosine Transform)
     */
    fun calculatePerceptualHash(bitmap: Bitmap): Long {
        // 1. Resize to 32x32
        val resized = Bitmap.createScaledBitmap(bitmap, DCT_SIZE, DCT_SIZE, false)
        
        // 2. Convert to grayscale
        val grayValues = Array(DCT_SIZE) { DoubleArray(DCT_SIZE) }
        for (x in 0 until DCT_SIZE) {
            for (y in 0 until DCT_SIZE) {
                val pixel = resized.getPixel(x, y)
                grayValues[x][y] = toGrayscale(pixel).toDouble()
            }
        }
        
        // 3. Apply DCT
        val dctValues = applyDCT(grayValues)
        
        // 4. Calculate average of top-left 8x8 (excluding DC component)
        var total = 0.0
        for (x in 0 until HASH_SIZE) {
            for (y in 0 until HASH_SIZE) {
                total += dctValues[x][y]
            }
        }
        total -= dctValues[0][0] // Exclude DC component
        val average = total / (HASH_SIZE * HASH_SIZE - 1)
        
        // 5. Generate hash
        var hash = 0L
        for (x in 0 until HASH_SIZE) {
            for (y in 0 until HASH_SIZE) {
                if (x != 0 || y != 0) { // Skip DC component
                    hash = hash shl 1
                    if (dctValues[x][y] > average) {
                        hash = hash or 1
                    }
                }
            }
        }
        
        resized.recycle()
        return hash
    }
    
    /**
     * Calculate Hamming distance between two hashes
     * Lower distance means more similar images
     */
    fun hammingDistance(hash1: Long, hash2: Long): Int {
        return (hash1 xor hash2).countOneBits()
    }
    
    /**
     * Calculate similarity percentage (0-100%)
     */
    fun calculateSimilarity(hash1: Long, hash2: Long, hashBits: Int = 64): Double {
        val distance = hammingDistance(hash1, hash2)
        return ((hashBits - distance).toDouble() / hashBits) * 100.0
    }
    
    /**
     * Check if two images are similar based on threshold
     */
    fun areSimilar(hash1: Long, hash2: Long, threshold: Int = 10): Boolean {
        return hammingDistance(hash1, hash2) <= threshold
    }
    
    /**
     * Convert RGB pixel to grayscale
     */
    private fun toGrayscale(pixel: Int): Int {
        val r = (pixel shr 16) and 0xFF
        val g = (pixel shr 8) and 0xFF
        val b = pixel and 0xFF
        return (0.299 * r + 0.587 * g + 0.114 * b).toInt()
    }
    
    /**
     * Apply Discrete Cosine Transform (DCT)
     */
    private fun applyDCT(input: Array<DoubleArray>): Array<DoubleArray> {
        val size = input.size
        val output = Array(size) { DoubleArray(size) }
        
        for (u in 0 until size) {
            for (v in 0 until size) {
                var sum = 0.0
                for (i in 0 until size) {
                    for (j in 0 until size) {
                        sum += cos(((2 * i + 1) * u * PI) / (2.0 * size)) *
                               cos(((2 * j + 1) * v * PI) / (2.0 * size)) *
                               input[i][j]
                    }
                }
                
                val cu = if (u == 0) 1.0 / sqrt(2.0) else 1.0
                val cv = if (v == 0) 1.0 / sqrt(2.0) else 1.0
                
                output[u][v] = 0.25 * cu * cv * sum
            }
        }
        
        return output
    }
}

/**
 * Data class to store image hash information
 */
data class ImageHash(
    val filePath: String,
    val averageHash: Long,
    val differenceHash: Long,
    val perceptualHash: Long
) {
    /**
     * Calculate similarity with another image hash
     */
    fun calculateSimilarity(other: ImageHash): ImageSimilarity {
        return ImageSimilarity(
            averageHashSimilarity = ImageHashUtils.calculateSimilarity(averageHash, other.averageHash),
            differenceHashSimilarity = ImageHashUtils.calculateSimilarity(differenceHash, other.differenceHash),
            perceptualHashSimilarity = ImageHashUtils.calculateSimilarity(perceptualHash, other.perceptualHash, 63) // pHash uses 63 bits
        )
    }
    
    /**
     * Check if similar to another image using multiple algorithms
     */
    fun isSimilarTo(other: ImageHash, threshold: Int = 10): Boolean {
        return ImageHashUtils.areSimilar(averageHash, other.averageHash, threshold) ||
               ImageHashUtils.areSimilar(differenceHash, other.differenceHash, threshold) ||
               ImageHashUtils.areSimilar(perceptualHash, other.perceptualHash, threshold)
    }
}

/**
 * Data class to store similarity results
 */
data class ImageSimilarity(
    val averageHashSimilarity: Double,
    val differenceHashSimilarity: Double,
    val perceptualHashSimilarity: Double
) {
    /**
     * Get the highest similarity score
     */
    val maxSimilarity: Double
        get() = maxOf(averageHashSimilarity, differenceHashSimilarity, perceptualHashSimilarity)
    
    /**
     * Get average similarity across all algorithms
     */
    val averageSimilarity: Double
        get() = (averageHashSimilarity + differenceHashSimilarity + perceptualHashSimilarity) / 3.0
}
