package com.example.clean0522.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

/**
 * Utility class for app management functionality
 */
object AppManagerUtils {

    /**
     * Data class representing an installed app
     */
    data class AppInfo(
        val packageName: String,
        val appName: String,
        val icon: Drawable?,
        val isSystemApp: Boolean,
        val versionName: String,
        val versionCode: Long,
        val installTime: Long,
        val updateTime: Long,
        val size: Long = 0L, // App size in bytes
        val targetSdkVersion: Int,
        val minSdkVersion: Int = 0
    ) {
        val installDate: String
            get() = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(installTime))

        val sizeFormatted: String
            get() = formatFileSize(size)
    }

    /**
     * App filter types
     */
    enum class AppFilter {
        ALL,
        SYSTEM_APPS,
        INSTALLED_APPS
    }

    /**
     * Get all installed apps based on filter
     */
    suspend fun getAllApps(context: Context, filter: AppFilter = AppFilter.ALL): List<AppInfo> = withContext(Dispatchers.IO) {
        try {
            val packageManager = context.packageManager
            val packages = packageManager.getInstalledPackages(PackageManager.GET_META_DATA)
            val apps = mutableListOf<AppInfo>()

            for (packageInfo in packages) {
                try {
                    val applicationInfo = packageInfo.applicationInfo ?: continue
                    val isSystemApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0

                    // Apply filter
                    when (filter) {
                        AppFilter.SYSTEM_APPS -> if (!isSystemApp) continue
                        AppFilter.INSTALLED_APPS -> if (isSystemApp) continue
                        AppFilter.ALL -> { /* Include all apps */ }
                    }

                    // Only include apps with launcher activities for user apps
                    if (!isSystemApp) {
                        val intent = Intent(Intent.ACTION_MAIN, null).apply {
                            addCategory(Intent.CATEGORY_LAUNCHER)
                            setPackage(packageInfo.packageName)
                        }
                        val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
                        if (resolveInfoList.isEmpty()) continue
                    }

                    val appName = try {
                        packageManager.getApplicationLabel(applicationInfo).toString()
                    } catch (e: Exception) {
                        packageInfo.packageName
                    }

                    val icon = try {
                        packageManager.getApplicationIcon(applicationInfo)
                    } catch (e: Exception) {
                        null
                    }

                    val appSize = try {
                        getAppSize(context, packageInfo.packageName)
                    } catch (e: Exception) {
                        0L
                    }

                    apps.add(
                        AppInfo(
                            packageName = packageInfo.packageName,
                            appName = appName,
                            icon = icon,
                            isSystemApp = isSystemApp,
                            versionName = packageInfo.versionName ?: "Unknown",
                            versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                                packageInfo.longVersionCode
                            } else {
                                @Suppress("DEPRECATION")
                                packageInfo.versionCode.toLong()
                            },
                            installTime = packageInfo.firstInstallTime,
                            updateTime = packageInfo.lastUpdateTime,
                            size = appSize,
                            targetSdkVersion = applicationInfo.targetSdkVersion,
                            minSdkVersion = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                                applicationInfo.minSdkVersion
                            } else {
                                0
                            }
                        )
                    )
                } catch (e: Exception) {
                    logE("Error getting app info for ${packageInfo.packageName}", e)
                }
            }

            apps.sortedBy { it.appName.lowercase() }
        } catch (e: Exception) {
            logE("Error getting all apps", e)
            emptyList()
        }
    }

    /**
     * Get app size (approximate)
     */
    private fun getAppSize(context: Context, packageName: String): Long {
        return try {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)

            // This is an approximation - actual app size calculation requires special permissions
            val sourceDir = applicationInfo.sourceDir
            if (sourceDir != null) {
                val file = java.io.File(sourceDir)
                if (file.exists()) file.length() else 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * Open app details/settings
     */
    fun openAppDetails(context: Context, packageName: String): Boolean {
        return try {
            val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = android.net.Uri.parse("package:$packageName")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            logE("Error opening app details for $packageName", e)
            false
        }
    }

    /**
     * Format file size to human readable format
     */
    private fun formatFileSize(bytes: Long): String {
        if (bytes < 1024) return "$bytes B"
        val kb = bytes / 1024.0
        if (kb < 1024) return String.format("%.1f KB", kb)
        val mb = kb / 1024.0
        if (mb < 1024) return String.format("%.1f MB", mb)
        val gb = mb / 1024.0
        return String.format("%.1f GB", gb)
    }

    /**
     * Get app count by filter
     */
    suspend fun getAppCount(context: Context, filter: AppFilter): Int = withContext(Dispatchers.IO) {
        try {
            val packageManager = context.packageManager
            val packages = packageManager.getInstalledPackages(0)
            var count = 0

            for (packageInfo in packages) {
                try {
                    val applicationInfo = packageInfo.applicationInfo ?: continue
                    val isSystemApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0

                    when (filter) {
                        AppFilter.SYSTEM_APPS -> if (isSystemApp) count++
                        AppFilter.INSTALLED_APPS -> {
                            if (!isSystemApp) {
                                // Only count user apps with launcher activities
                                val intent = Intent(Intent.ACTION_MAIN, null).apply {
                                    addCategory(Intent.CATEGORY_LAUNCHER)
                                    setPackage(packageInfo.packageName)
                                }
                                val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
                                if (resolveInfoList.isNotEmpty()) count++
                            }
                        }
                        AppFilter.ALL -> {
                            if (isSystemApp) {
                                count++
                            } else {
                                // Only count user apps with launcher activities
                                val intent = Intent(Intent.ACTION_MAIN, null).apply {
                                    addCategory(Intent.CATEGORY_LAUNCHER)
                                    setPackage(packageInfo.packageName)
                                }
                                val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
                                if (resolveInfoList.isNotEmpty()) count++
                            }
                        }
                    }
                } catch (e: Exception) {
                    // Skip apps that can't be queried
                }
            }

            count
        } catch (e: Exception) {
            logE("Error getting app count for filter $filter", e)
            0
        }
    }
}
