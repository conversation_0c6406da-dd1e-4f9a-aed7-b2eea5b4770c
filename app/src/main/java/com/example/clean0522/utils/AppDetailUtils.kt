package com.example.clean0522.utils

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager

import android.graphics.drawable.Drawable
import android.os.Build
import android.util.Log
import java.io.ByteArrayInputStream
import java.security.MessageDigest
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.text.SimpleDateFormat
import java.util.*

/**
 * Utility class for extracting detailed app information
 */
object AppDetailUtils {

    /**
     * Data class for detailed app information
     */
    data class AppDetailInfo(
        val packageName: String,
        val appName: String,
        val icon: Drawable?,
        val versionName: String,
        val versionCode: Long,
        val apkSize: Long,
        val minSdkVersion: Int,
        val targetSdkVersion: Int,
        val processName: String,
        val isSystemApp: Boolean,
        val permissions: List<AppPermissionInfo>,
        val certificates: List<CertificateInfo>
    )

    /**
     * Data class for permission information
     */
    data class AppPermissionInfo(
        val name: String,
        val description: String,
        val isGranted: Boolean,
        val protectionLevel: String
    )

    /**
     * Data class for certificate information
     */
    data class CertificateInfo(
        val signatureAlgorithm: String,
        val validFrom: String,
        val validTo: String,
        val publicKeyMD5: String,
        val certificateMD5: String,
        val serialNumber: String,
        val issuerName: String,
        val issuerOrganization: String,
        val issuerCountry: String,
        val subjectName: String,
        val subjectOrganization: String,
        val subjectCountry: String,
        val version: Int
    )

    /**
     * Get detailed app information by package name
     */
    fun getAppDetailInfo(context: Context, packageName: String): AppDetailInfo? {
        return try {
            val packageManager = context.packageManager
            val flags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                PackageManager.GET_PERMISSIONS or PackageManager.GET_SIGNING_CERTIFICATES
            } else {
                @Suppress("DEPRECATION")
                PackageManager.GET_PERMISSIONS or PackageManager.GET_SIGNATURES
            }
            val packageInfo = packageManager.getPackageInfo(packageName, flags)
            val applicationInfo = packageInfo.applicationInfo
            if (applicationInfo == null) {
                logE("ApplicationInfo is null for package: $packageName")
                return null
            }

            // Get app name and icon
            val appName = packageManager.getApplicationLabel(applicationInfo).toString()
            val icon = try {
                packageManager.getApplicationIcon(applicationInfo)
            } catch (e: Exception) {
                null
            }

            // Get APK size
            val apkSize = try {
                val apkPath = applicationInfo.sourceDir
                val file = java.io.File(apkPath)
                file.length()
            } catch (e: Exception) {
                0L
            }

            // Get version info
            val versionName = packageInfo.versionName ?: "Unknown"
            val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }

            // Get SDK versions
            val minSdkVersion = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                applicationInfo.minSdkVersion
            } else {
                0
            }
            val targetSdkVersion = applicationInfo.targetSdkVersion

            // Get process name
            val processName = applicationInfo.processName ?: packageName

            // Check if system app
            val isSystemApp = (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0

            // Get permissions
            val permissions = getAppPermissions(context, packageInfo)

            // Get certificates - try main method first, then fallback
            var certificates = getAppCertificates(packageInfo)
            if (certificates.isEmpty()) {
                logE("Main certificate extraction failed, trying alternative method")
                certificates = getCertificatesForPackage(context, packageName)
            }

            AppDetailInfo(
                packageName = packageName,
                appName = appName,
                icon = icon,
                versionName = versionName,
                versionCode = versionCode,
                apkSize = apkSize,
                minSdkVersion = minSdkVersion,
                targetSdkVersion = targetSdkVersion,
                processName = processName,
                isSystemApp = isSystemApp,
                permissions = permissions,
                certificates = certificates
            )
        } catch (e: Exception) {
            logE("Error getting app detail info for $packageName", e)
            null
        }
    }

    /**
     * Get app permissions
     */
    private fun getAppPermissions(context: Context, packageInfo: PackageInfo): List<AppPermissionInfo> {
        val permissions = mutableListOf<AppPermissionInfo>()
        val packageManager = context.packageManager

        packageInfo.requestedPermissions?.forEachIndexed { index, permission ->
            try {
                val permissionInfo = packageManager.getPermissionInfo(permission, 0)
                val description = permissionInfo.loadDescription(packageManager)?.toString() ?: permission

                val isGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    packageInfo.requestedPermissionsFlags?.get(index)?.let { flags ->
                        (flags and PackageInfo.REQUESTED_PERMISSION_GRANTED) != 0
                    } ?: false
                } else {
                    true // Pre-M permissions are granted at install time
                }

                val protectionLevel = when (permissionInfo.protectionLevel and android.content.pm.PermissionInfo.PROTECTION_MASK_BASE) {
                    android.content.pm.PermissionInfo.PROTECTION_NORMAL -> "Normal"
                    android.content.pm.PermissionInfo.PROTECTION_DANGEROUS -> "Dangerous"
                    android.content.pm.PermissionInfo.PROTECTION_SIGNATURE -> "Signature"
                    else -> "Unknown"
                }

                permissions.add(
                    AppPermissionInfo(
                        name = permission,
                        description = description,
                        isGranted = isGranted,
                        protectionLevel = protectionLevel
                    )
                )
            } catch (e: Exception) {
                // Permission not found, add basic info
                permissions.add(
                    AppPermissionInfo(
                        name = permission,
                        description = permission,
                        isGranted = false,
                        protectionLevel = "Unknown"
                    )
                )
            }
        }

        return permissions
    }

    /**
     * Get app certificates
     */
    private fun getAppCertificates(packageInfo: PackageInfo): List<CertificateInfo> {
        val certificates = mutableListOf<CertificateInfo>()
        val dateFormat = SimpleDateFormat("dd/MM/yy HH:mmaa", Locale.getDefault())

        try {
            val signatures = getSignatures(packageInfo)
            logE("Found ${signatures?.size ?: 0} signatures for package ${packageInfo.packageName}")

            signatures?.forEach { signature ->
                try {
                    val certFactory = CertificateFactory.getInstance("X.509")
                    val cert = certFactory.generateCertificate(
                        ByteArrayInputStream(signature.toByteArray())
                    ) as X509Certificate

                    logE("Successfully parsed certificate for ${packageInfo.packageName}")

                    // Calculate MD5 hashes
                    val publicKeyMD5 = calculateMD5(cert.publicKey.encoded)
                    val certificateMD5 = calculateMD5(cert.encoded)

                    // Parse subject and issuer
                    val subjectParts = parseDN(cert.subjectDN.name)
                    val issuerParts = parseDN(cert.issuerDN.name)

                    certificates.add(
                        CertificateInfo(
                            signatureAlgorithm = cert.sigAlgName,
                            validFrom = dateFormat.format(cert.notBefore),
                            validTo = dateFormat.format(cert.notAfter),
                            publicKeyMD5 = publicKeyMD5,
                            certificateMD5 = certificateMD5,
                            serialNumber = cert.serialNumber.toString(),
                            issuerName = issuerParts["CN"] ?: "",
                            issuerOrganization = issuerParts["O"] ?: "",
                            issuerCountry = issuerParts["C"] ?: "",
                            subjectName = subjectParts["CN"] ?: "",
                            subjectOrganization = subjectParts["O"] ?: "",
                            subjectCountry = subjectParts["C"] ?: "",
                            version = cert.version
                        )
                    )
                } catch (e: Exception) {
                    logE("Error parsing certificate", e)
                }
            }
        } catch (e: Exception) {
            logE("Error getting certificates", e)
        }

        logE("Returning ${certificates.size} certificates")
        return certificates
    }

    /**
     * Get signatures from PackageInfo handling different Android versions
     */
    private fun getSignatures(packageInfo: PackageInfo): Array<android.content.pm.Signature>? {
        logE("Getting signatures for ${packageInfo.packageName}, Android API: ${Build.VERSION.SDK_INT}")

        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android P and above - try new API first
                val signingInfo = packageInfo.signingInfo
                logE("SigningInfo: $signingInfo")

                if (signingInfo != null) {
                    val result = if (signingInfo.hasMultipleSigners()) {
                        // Multiple signers (APK Signature Scheme v2/v3)
                        logE("App has multiple signers")
                        signingInfo.apkContentsSigners
                    } else {
                        // Single signer
                        logE("App has single signer")
                        signingInfo.signingCertificateHistory
                    }

                    if (result != null && result.isNotEmpty()) {
                        logE("Found ${result.size} signatures using new API")
                        return result
                    } else {
                        logE("New API returned empty signatures, trying fallback")
                    }
                }

                // Fallback to legacy method for Android P+
                logE("Trying legacy method as fallback for Android P+")
                @Suppress("DEPRECATION")
                val legacySignatures = packageInfo.signatures
                logE("Legacy fallback found ${legacySignatures?.size ?: 0} signatures")
                legacySignatures
            } else {
                // Android O and below
                @Suppress("DEPRECATION")
                val signatures = packageInfo.signatures
                logE("Using legacy signatures for Android O-, found ${signatures?.size ?: 0}")
                signatures
            }
        } catch (e: Exception) {
            logE("Error getting signatures: ${e.message}", e)

            // Final fallback - try to get package info again with different flags
            try {
                logE("Attempting final fallback with different flags")
                val context = packageInfo.applicationInfo?.let {
                    // We don't have direct access to context here, so this is a limitation
                    null
                }

                // Just return null if we can't get signatures
                null
            } catch (e2: Exception) {
                logE("All signature retrieval methods failed", e2)
                null
            }
        }
    }

    /**
     * Alternative method to get certificates with more comprehensive error handling
     */
    fun getCertificatesForPackage(context: Context, packageName: String): List<CertificateInfo> {
        logE("Getting certificates for package: $packageName")

        val packageManager = context.packageManager
        val certificates = mutableListOf<CertificateInfo>()

        // Try different flag combinations
        val flagCombinations = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            listOf(
                PackageManager.GET_SIGNING_CERTIFICATES,
                @Suppress("DEPRECATION") PackageManager.GET_SIGNATURES,
                PackageManager.GET_SIGNING_CERTIFICATES or PackageManager.GET_PERMISSIONS,
                @Suppress("DEPRECATION") (PackageManager.GET_SIGNATURES or PackageManager.GET_PERMISSIONS)
            )
        } else {
            listOf(
                @Suppress("DEPRECATION") PackageManager.GET_SIGNATURES,
                @Suppress("DEPRECATION") (PackageManager.GET_SIGNATURES or PackageManager.GET_PERMISSIONS)
            )
        }

        for (flags in flagCombinations) {
            try {
                logE("Trying flags: $flags")
                val packageInfo = packageManager.getPackageInfo(packageName, flags)
                val extractedCerts = getAppCertificates(packageInfo)

                if (extractedCerts.isNotEmpty()) {
                    logE("Successfully extracted ${extractedCerts.size} certificates with flags: $flags")
                    return extractedCerts
                } else {
                    logE("No certificates found with flags: $flags")
                }
            } catch (e: Exception) {
                logE("Failed with flags $flags: ${e.message}")
            }
        }

        logE("All attempts to get certificates failed for package: $packageName")
        return certificates
    }

    /**
     * Calculate MD5 hash of byte array
     */
    private fun calculateMD5(data: ByteArray): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(data)
            digest.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * Parse Distinguished Name (DN) into components
     */
    private fun parseDN(dn: String): Map<String, String> {
        val result = mutableMapOf<String, String>()
        try {
            // Split by comma and parse each component
            dn.split(",").forEach { component ->
                val trimmed = component.trim()
                val equalIndex = trimmed.indexOf("=")
                if (equalIndex > 0) {
                    val key = trimmed.substring(0, equalIndex).trim()
                    val value = trimmed.substring(equalIndex + 1).trim()
                    result[key] = value
                }
            }
        } catch (e: Exception) {
            // Ignore parsing errors
        }
        return result
    }

    /**
     * Format file size
     */
    fun formatFileSize(size: Long): String {
        return FileUtils.formatFileSize(size)
    }

    /**
     * Debug method to test certificate extraction for a specific package
     */
    fun debugCertificates(context: Context, packageName: String) {
        logE("=== DEBUG CERTIFICATES FOR $packageName ===")

        try {
            val packageManager = context.packageManager

            // Test different flag combinations
            val testFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                mapOf(
                    "GET_SIGNING_CERTIFICATES" to PackageManager.GET_SIGNING_CERTIFICATES,
                    "GET_SIGNATURES (deprecated)" to PackageManager.GET_SIGNATURES,
                    "GET_SIGNING_CERTIFICATES + GET_PERMISSIONS" to (PackageManager.GET_SIGNING_CERTIFICATES or PackageManager.GET_PERMISSIONS),
                    "GET_SIGNATURES + GET_PERMISSIONS (deprecated)" to (PackageManager.GET_SIGNATURES or PackageManager.GET_PERMISSIONS)
                )
            } else {
                mapOf(
                    "GET_SIGNATURES" to PackageManager.GET_SIGNATURES,
                    "GET_SIGNATURES + GET_PERMISSIONS" to (PackageManager.GET_SIGNATURES or PackageManager.GET_PERMISSIONS)
                )
            }

            for ((name, flags) in testFlags) {
                try {
                    logE("Testing with $name (flags: $flags)")
                    val packageInfo = packageManager.getPackageInfo(packageName, flags)

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                        val signingInfo = packageInfo.signingInfo
                        logE("  SigningInfo: $signingInfo")

                        if (signingInfo != null) {
                            logE("  Has multiple signers: ${signingInfo.hasMultipleSigners()}")

                            val apkSigners = signingInfo.apkContentsSigners
                            val certHistory = signingInfo.signingCertificateHistory

                            logE("  APK content signers: ${apkSigners?.size ?: 0}")
                            logE("  Certificate history: ${certHistory?.size ?: 0}")
                        }
                    }

                    @Suppress("DEPRECATION")
                    val legacySignatures = packageInfo.signatures
                    logE("  Legacy signatures: ${legacySignatures?.size ?: 0}")

                    val certificates = getAppCertificates(packageInfo)
                    logE("  Extracted certificates: ${certificates.size}")

                    if (certificates.isNotEmpty()) {
                        logE("  SUCCESS with $name!")
                        certificates.forEachIndexed { index, cert ->
                            logE("    Certificate $index:")
                            logE("      Algorithm: ${cert.signatureAlgorithm}")
                            logE("      Subject: ${cert.subjectName}")
                            logE("      Issuer: ${cert.issuerName}")
                        }
                        break
                    }

                } catch (e: Exception) {
                    logE("  FAILED with $name: ${e.message}")
                }
            }

        } catch (e: Exception) {
            logE("Debug certificates failed: ${e.message}", e)
        }

        logE("=== END DEBUG CERTIFICATES ===")
    }
}
