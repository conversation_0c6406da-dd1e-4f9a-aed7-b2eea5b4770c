package com.example.clean0522.utils

import android.util.Log
import com.example.clean0522.BuildConfig

/**
 * Unified logging utility with debug mode control
 * Only logs in debug builds, silent in release builds
 */
object LogUtils {
    
    private const val DEFAULT_TAG = "Clean0522"
    
    /**
     * Check if logging is enabled (only in debug builds)
     */
    private val isLoggingEnabled: Boolean
        get() = BuildConfig.DEBUG
    
    /**
     * Log debug message
     */
    fun d(tag: String = DEFAULT_TAG, message: String) {
        if (isLoggingEnabled) {
            Log.d(tag, message)
        }
    }
    
    /**
     * Log info message
     */
    fun i(tag: String = DEFAULT_TAG, message: String) {
        if (isLoggingEnabled) {
            Log.i(tag, message)
        }
    }
    
    /**
     * Log warning message
     */
    fun w(tag: String = DEFAULT_TAG, message: String) {
        if (isLoggingEnabled) {
            Log.w(tag, message)
        }
    }
    
    /**
     * Log warning message with throwable
     */
    fun w(tag: String = DEFAULT_TAG, message: String, throwable: Throwable) {
        if (isLoggingEnabled) {
            Log.w(tag, message, throwable)
        }
    }
    
    /**
     * Log error message
     */
    fun e(tag: String = DEFAULT_TAG, message: String) {
        if (isLoggingEnabled) {
            Log.e(tag, message)
        }
    }
    
    /**
     * Log error message with throwable
     */
    fun e(tag: String = DEFAULT_TAG, message: String, throwable: Throwable) {
        if (isLoggingEnabled) {
            Log.e(tag, message, throwable)
        }
    }
    
    /**
     * Log verbose message
     */
    fun v(tag: String = DEFAULT_TAG, message: String) {
        if (isLoggingEnabled) {
            Log.v(tag, message)
        }
    }
    
    /**
     * Log what a method is doing (for debugging method flow)
     */
    fun method(tag: String = DEFAULT_TAG, methodName: String, message: String = "") {
        if (isLoggingEnabled) {
            val fullMessage = if (message.isNotEmpty()) {
                "$methodName: $message"
            } else {
                "$methodName called"
            }
            Log.d(tag, fullMessage)
        }
    }
    
    /**
     * Log performance timing
     */
    fun performance(tag: String = DEFAULT_TAG, operation: String, timeMs: Long) {
        if (isLoggingEnabled) {
            Log.d(tag, "⏱️ $operation took ${timeMs}ms")
        }
    }
    
    /**
     * Log memory usage
     */
    fun memory(tag: String = DEFAULT_TAG, context: String) {
        if (isLoggingEnabled) {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val usagePercent = (usedMemory * 100 / maxMemory)
            
            Log.d(tag, "🧠 $context - Memory: ${formatBytes(usedMemory)}/${formatBytes(maxMemory)} (${usagePercent}%)")
        }
    }
    
    /**
     * Log with custom emoji prefix for easy identification
     */
    fun custom(tag: String = DEFAULT_TAG, emoji: String, message: String) {
        if (isLoggingEnabled) {
            Log.d(tag, "$emoji $message")
        }
    }
    
    /**
     * Format bytes to human readable format
     */
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return "%.1f%s".format(size, units[unitIndex])
    }
}

/**
 * Extension functions for easier logging
 */

/**
 * Log debug message with automatic tag from class name
 */
fun Any.logD(message: String) {
    LogUtils.d(this::class.java.simpleName, message)
}

fun Any.logD(tag: String, message: String) {
    LogUtils.w(tag, message)
}

/**
 * Log info message with automatic tag from class name
 */
fun Any.logI(message: String) {
    LogUtils.i(this::class.java.simpleName, message)
}

/**
 * Log warning message with automatic tag from class name
 */
fun Any.logW(message: String) {
    LogUtils.w(this::class.java.simpleName, message)
}

/**
 * Log warning message with throwable and automatic tag from class name
 */
fun Any.logW(message: String, throwable: Throwable) {
    LogUtils.w(this::class.java.simpleName, message, throwable)
}

/**
 * Log error message with automatic tag from class name
 */
fun Any.logE(message: String) {
    LogUtils.e(this::class.java.simpleName, message)
}

fun Any.logE(tag: String, message: String, e: Exception) {
    LogUtils.e(tag, message, e)
}

/**
 * Log error message with throwable and automatic tag from class name
 */
fun Any.logE(message: String, throwable: Throwable) {
    LogUtils.e(this::class.java.simpleName, message, throwable)
}

/**
 * Log verbose message with automatic tag from class name
 */
fun Any.logV(message: String) {
    LogUtils.v(this::class.java.simpleName, message)
}

/**
 * Log method call with automatic tag from class name
 */
fun Any.logMethod(methodName: String, message: String = "") {
    LogUtils.method(this::class.java.simpleName, methodName, message)
}

/**
 * Log performance timing with automatic tag from class name
 */
fun Any.logPerformance(operation: String, timeMs: Long) {
    LogUtils.performance(this::class.java.simpleName, operation, timeMs)
}

/**
 * Log memory usage with automatic tag from class name
 */
fun Any.logMemory(context: String) {
    LogUtils.memory(this::class.java.simpleName, context)
}

/**
 * Log with custom emoji and automatic tag from class name
 */
fun Any.logCustom(emoji: String, message: String) {
    LogUtils.custom(this::class.java.simpleName, emoji, message)
}
