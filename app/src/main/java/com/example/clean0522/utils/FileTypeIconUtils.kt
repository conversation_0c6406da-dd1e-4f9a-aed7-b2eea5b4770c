package com.example.clean0522.utils

import androidx.annotation.DrawableRes
import com.example.clean0522.R
import com.example.clean0522.domain.model.FileItem
import java.io.File

/**
 * Utility class for determining file type icons
 */
object FileTypeIconUtils {
    
    /**
     * Get the appropriate icon resource for a file item
     */
    @DrawableRes
    fun getFileTypeIcon(fileItem: FileItem): Int {
        return when {
            fileItem.isDirectory -> R.mipmap.type_folder
            fileItem.isImage -> R.mipmap.type_image
            fileItem.isVideo -> R.mipmap.type_video
            fileItem.isAudio -> R.mipmap.type_audio
            fileItem.isDocument -> getDocumentTypeIcon(fileItem.extension)
            fileItem.isArchive -> R.mipmap.type_zip
            fileItem.isApk -> R.mipmap.type_apk
            else -> R.mipmap.type_unknown
        }
    }
    
    /**
     * Get the appropriate icon resource for a file by extension
     */
    @DrawableRes
    fun getFileTypeIconByExtension(extension: String): Int {
        val lowerExtension = extension.lowercase()
        return when {
            isImageExtension(lowerExtension) -> R.mipmap.type_image
            isVideoExtension(lowerExtension) -> R.mipmap.type_video
            isAudioExtension(lowerExtension) -> R.mipmap.type_audio
            isDocumentExtension(lowerExtension) -> getDocumentTypeIcon(lowerExtension)
            isArchiveExtension(lowerExtension) -> R.mipmap.type_zip
            isApkExtension(lowerExtension) -> R.mipmap.type_apk
            else -> R.mipmap.type_unknown
        }
    }
    
    /**
     * Get the appropriate icon resource for a file
     */
    @DrawableRes
    fun getFileTypeIcon(file: File): Int {
        return when {
            file.isDirectory -> R.mipmap.type_folder
            isImageFile(file) -> R.mipmap.type_image
            isVideoFile(file) -> R.mipmap.type_video
            isAudioFile(file) -> R.mipmap.type_audio
            isDocumentFile(file) -> getDocumentTypeIcon(file.extension)
            isArchiveFile(file) -> R.mipmap.type_zip
            isApkFile(file) -> R.mipmap.type_apk
            else -> R.mipmap.type_unknown
        }
    }
    
    /**
     * Get specific document type icon based on extension
     */
    @DrawableRes
    private fun getDocumentTypeIcon(extension: String): Int {
        return when (extension.lowercase()) {
            "pdf" -> R.mipmap.type_pdf
            "doc", "docx" -> R.mipmap.type_word
            "xls", "xlsx" -> R.mipmap.type_excel
            "ppt", "pptx" -> R.mipmap.type_ppt
            "txt", "rtf" -> R.mipmap.type_txt
            else -> R.mipmap.type_word
        }
    }
    
    // File type checking methods
    private fun isImageFile(file: File): Boolean {
        return isImageExtension(file.extension.lowercase())
    }
    
    private fun isVideoFile(file: File): Boolean {
        return isVideoExtension(file.extension.lowercase())
    }
    
    private fun isAudioFile(file: File): Boolean {
        return isAudioExtension(file.extension.lowercase())
    }
    
    private fun isDocumentFile(file: File): Boolean {
        return isDocumentExtension(file.extension.lowercase())
    }
    
    private fun isArchiveFile(file: File): Boolean {
        return isArchiveExtension(file.extension.lowercase())
    }
    
    private fun isApkFile(file: File): Boolean {
        return isApkExtension(file.extension.lowercase())
    }
    
    // Extension checking methods
    private fun isImageExtension(extension: String): Boolean {
        val imageExtensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "svg")
        return imageExtensions.contains(extension)
    }
    
    private fun isVideoExtension(extension: String): Boolean {
        val videoExtensions = listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp")
        return videoExtensions.contains(extension)
    }
    
    private fun isAudioExtension(extension: String): Boolean {
        val audioExtensions = listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a")
        return audioExtensions.contains(extension)
    }
    
    private fun isDocumentExtension(extension: String): Boolean {
        val docExtensions = listOf("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rtf")
        return docExtensions.contains(extension)
    }
    
    private fun isArchiveExtension(extension: String): Boolean {
        val archiveExtensions = listOf("zip", "rar", "7z", "tar", "gz", "bz2", "xz")
        return archiveExtensions.contains(extension)
    }
    
    private fun isApkExtension(extension: String): Boolean {
        return extension == "apk"
    }
}
