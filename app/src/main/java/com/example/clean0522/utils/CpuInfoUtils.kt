package com.example.clean0522.utils

import android.content.Context
import android.os.Build
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.InputStreamReader
import com.example.clean0522.R
import com.example.clean0522.viewmodel.CpuInfoViewModel

/**
 * Utility class for CPU and GPU information monitoring
 */
object CpuInfoUtils {

    /**
     * Data class for CPU core information
     */
    data class CpuCoreInfo(
        val coreIndex: Int,
        val currentFrequency: Long, // in KHz
        val maxFrequency: Long,     // in KHz
        val minFrequency: Long      // in KHz
    ) {
        val currentFrequencyMHz: Int
            get() = (currentFrequency / 1000).toInt()

        val currentFrequencyGHz: Float
            get() = currentFrequency / 1000000f
    }

    /**
     * Data class for CPU information
     */
    data class CpuInfo(
        val cores: List<CpuCoreInfo>,
        val governor: String,
        val hardware: String,
        val fabrication: String,
        val processor: String,
        val supportedAbis: String,
        val timestamp: Long = System.currentTimeMillis()
    )

    /**
     * Get current CPU information
     */
    fun getCurrentCpuInfo(): CpuInfo {
        val cores = getCpuCores()
        val governor = getCpuGovernor()
        val hardware = getHardwareInfo()
        val fabrication = getFabricationInfo()
        val processor = getProcessorInfo()
        val supportedAbis = getSupportedAbis()

        return CpuInfo(
            cores = cores,
            governor = governor,
            hardware = hardware,
            fabrication = fabrication,
            processor = processor,
            supportedAbis = supportedAbis
        )
    }

    /**
     * Get CPU cores information
     */
    private fun getCpuCores(): List<CpuCoreInfo> {
        val cores = mutableListOf<CpuCoreInfo>()
        var coreIndex = 0

        while (true) {
            val cpuDir = File("/sys/devices/system/cpu/cpu$coreIndex")
            if (!cpuDir.exists()) break

            val currentFreq = readCpuFrequency("/sys/devices/system/cpu/cpu$coreIndex/cpufreq/scaling_cur_freq")
            val maxFreq = readCpuFrequency("/sys/devices/system/cpu/cpu$coreIndex/cpufreq/cpuinfo_max_freq")
            val minFreq = readCpuFrequency("/sys/devices/system/cpu/cpu$coreIndex/cpufreq/cpuinfo_min_freq")

            cores.add(
                CpuCoreInfo(
                    coreIndex = coreIndex + 1, // Display as 1-based
                    currentFrequency = currentFreq,
                    maxFrequency = maxFreq,
                    minFrequency = minFreq
                )
            )
            coreIndex++
        }

        // If no cores found through sysfs, create default cores based on processor count
        if (cores.isEmpty()) {
            val processorCount = Runtime.getRuntime().availableProcessors()
            repeat(processorCount) { index ->
                cores.add(
                    CpuCoreInfo(
                        coreIndex = index + 1,
                        currentFrequency = 1800000, // Default 1.8 GHz
                        maxFrequency = 2400000,     // Default 2.4 GHz
                        minFrequency = 300000       // Default 300 MHz
                    )
                )
            }
        }

        return cores
    }

    /**
     * Read CPU frequency from file
     */
    private fun readCpuFrequency(path: String): Long {
        return try {
            val file = File(path)
            if (file.exists()) {
                file.readText().trim().toLongOrNull() ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * Get CPU governor
     */
    private fun getCpuGovernor(): String {
        return try {
            val file = File("/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor")
            if (file.exists()) {
                file.readText().trim()
            } else {
                "N/A"
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get hardware information
     */
    private fun getHardwareInfo(): String {
        return try {
            readFromProcCpuInfo("Hardware") ?: Build.HARDWARE ?: "N/A"
        } catch (e: Exception) {
            Build.HARDWARE ?: "N/A"
        }
    }

    /**
     * Get fabrication information
     */
    private fun getFabricationInfo(): String {
        return try {
            // Try to get from various sources
            readFromProcCpuInfo("CPU implementer")?.let { implementer ->
                when (implementer.trim()) {
                    "0x41" -> "ARM"
                    "0x51" -> "Qualcomm"
                    "0x53" -> "Samsung"
                    else -> "N/A"
                }
                } ?: "N/A"
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get processor information
     */
    private fun getProcessorInfo(): String {
        return try {
            val processorName = getProcessorName()
            val cpuBit = getCpuBit()

            if (processorName.isNotEmpty()) {
                "$processorName $cpuBit"
            } else {
                cpuBit
            }
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Get processor name from /proc/cpuinfo
     */
    private fun getProcessorName(): String {
        var cpuName = ""
        try {
            val process = ProcessBuilder()
                .command("/system/bin/cat", "/proc/cpuinfo")
                .redirectErrorStream(true)
                .start()

            val inputStream = process.inputStream
            val reader = BufferedReader(InputStreamReader(inputStream))
            var line: String?

            while (reader.readLine().also { line = it } != null) {
                if (line?.startsWith("Processor") == true) {
                    if (line.isNotBlank()) {
                        cpuName = line.substring(line.indexOf(":") + 1)?.trim() ?: ""
                    }
                    break
                }
            }

            reader.close()
            inputStream.close()
            process.waitFor()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cpuName
    }

    /**
     * Get CPU bit architecture
     */
    private fun getCpuBit(): String {
        return if (Build.SUPPORTED_64_BIT_ABIS.isNotEmpty()) "64bit" else "32bit"
    }

    /**
     * Get supported ABIs
     */
    private fun getSupportedAbis(): String {
        return try {
            Build.SUPPORTED_ABIS.joinToString(", ")
        } catch (e: Exception) {
            "N/A"
        }
    }

    /**
     * Read information from /proc/cpuinfo
     */
    private fun readFromProcCpuInfo(key: String): String? {
        return try {
            BufferedReader(FileReader("/proc/cpuinfo")).use { reader ->
                reader.lineSequence().forEach { line ->
                    if (line.startsWith(key, ignoreCase = true)) {
                        val parts = line.split(":")
                        if (parts.size >= 2) {
                            return parts[1].trim()
                        }
                    }
                }
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Get real-time CPU info flow that updates every 2 seconds
     */
    fun getCpuInfoFlow(): Flow<CpuInfo> = flow {
        while (true) {
            emit(getCurrentCpuInfo())
            delay(2000) // Update every 2 seconds
        }
    }

    /**
     * Get all CPU and GPU info items for display
     */
    fun getCpuGpuInfoItems(context: Context, cpuInfo: CpuInfo, gpuInfo: CpuInfoViewModel.GpuInfo): List<DeviceInfoData> {
        return listOf(
            DeviceInfoData(
                title = context.getString(R.string.cpu_governor),
                value = cpuInfo.governor,
                icon = R.mipmap.c_governor
            ),
            DeviceInfoData(
                title = context.getString(R.string.cpu_hardware),
                value = cpuInfo.hardware,
                icon = R.mipmap.c_hard
            ),
            DeviceInfoData(
                title = context.getString(R.string.cpu_frequency),
                value = getAverageFrequency(cpuInfo.cores, context),
                icon = R.mipmap.c_freq
            ),
            DeviceInfoData(
                title = context.getString(R.string.cpu_processor),
                value = cpuInfo.processor,
                icon = R.mipmap.c_processor
            ),
            DeviceInfoData(
                title = context.getString(R.string.cpu_supported_abis),
                value = cpuInfo.supportedAbis,
                icon = R.mipmap.c_abi
            ),

            DeviceInfoData(
                title = context.getString(R.string.gpu_renderer),
                value = gpuInfo.renderer,
                icon = R.mipmap.c_render
            ),
            DeviceInfoData(
                title = context.getString(R.string.gpu_vendor),
                value = gpuInfo.vendor,
                icon = R.mipmap.c_freq
            ),
            DeviceInfoData(
                title = context.getString(R.string.gpu_version),
                value = gpuInfo.version,
                icon = R.mipmap.c_version
            ),
            DeviceInfoData(
                title = context.getString(R.string.cpu_vulkan),
                value = gpuInfo.vulkanVersion,
                icon = R.mipmap.c_vulkan
            )
        )
    }

    private fun getAverageFrequency(cores: List<CpuCoreInfo>, context: Context): String {
        if (cores.isEmpty()) return context.getString(R.string.cpu_unknown)

        val clusters = mutableMapOf<Pair<Long, Long>, MutableList<CpuCoreInfo>>()

        cores.forEach { core ->
            val key = Pair(core.minFrequency, core.maxFrequency)
            clusters.getOrPut(key) { mutableListOf() }.add(core)
        }

        // Format each cluster
        val clusterStrings = clusters.map { (freqRange, coresInCluster) ->
            val count = coresInCluster.size
            val minFreqMHz = (freqRange.first / 1000).toInt()
            val maxFreqMHz = (freqRange.second / 1000).toInt()

            "$count x ${minFreqMHz}MHz-${maxFreqMHz}MHz"
        }

        return clusterStrings.joinToString(", \n")
    }
}
