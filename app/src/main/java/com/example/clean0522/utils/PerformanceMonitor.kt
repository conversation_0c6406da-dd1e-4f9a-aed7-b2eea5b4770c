package com.example.clean0522.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.system.measureTimeMillis

/**
 * Performance monitoring utility for debugging and optimization
 */
object PerformanceMonitor {

    const val TAG = "PerformanceMonitor"

    /**
     * Measure execution time of a suspend function
     */
    suspend inline fun <T> measureSuspend(
        operation: String,
        block: suspend () -> T
    ): T {
        var result: T
        val time = measureTimeMillis {
            result = block()
        }
        LogUtils.performance(TAG, operation, time)
        return result
    }

    /**
     * Measure execution time of a regular function
     */
    inline fun <T> measure(
        operation: String,
        block: () -> T
    ): T {
        var result: T
        val time = measureTimeMillis {
            result = block()
        }
        LogUtils.performance(TAG, operation, time)
        return result
    }

    /**
     * Log memory usage
     */
    fun logMemoryUsage(context: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val availableMemory = maxMemory - usedMemory

        LogUtils.memory(TAG, context)
    }

    /**
     * Start performance monitoring for a scope
     */
    fun startMonitoring(scope: CoroutineScope, intervalMs: Long = 5000) {
        scope.launch(Dispatchers.IO) {
            while (true) {
                logMemoryUsage("Periodic Check")
                kotlinx.coroutines.delay(intervalMs)
            }
        }
    }

    /**
     * Format bytes to human readable format
     */
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB")
        var size = bytes.toDouble()
        var unitIndex = 0

        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }

        return "%.2f %s".format(size, units[unitIndex])
    }

    /**
     * Performance statistics tracker
     */
    class PerformanceStats {
        private val measurements = mutableListOf<Long>()

        fun addMeasurement(timeMs: Long) {
            measurements.add(timeMs)
        }

        fun getStats(): String {
            if (measurements.isEmpty()) return "No measurements"

            val avg = measurements.average()
            val min = measurements.minOrNull() ?: 0
            val max = measurements.maxOrNull() ?: 0
            val total = measurements.sum()

            return """
                Performance Stats:
                - Count: ${measurements.size}
                - Total: ${total}ms
                - Average: ${"%.2f".format(avg)}ms
                - Min: ${min}ms
                - Max: ${max}ms
            """.trimIndent()
        }

        fun clear() {
            measurements.clear()
        }
    }
}
