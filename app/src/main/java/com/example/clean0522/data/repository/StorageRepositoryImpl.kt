package com.example.clean0522.data.repository

import android.content.Context
import android.os.Environment
import android.os.StatFs
import com.example.clean0522.domain.model.StorageInfo
import com.example.clean0522.domain.repository.StorageRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * Implementation of StorageRepository
 */
class StorageRepositoryImpl(
    private val context: Context
) : StorageRepository {
    
    override fun getStorageInfo(): Flow<StorageInfo> = flow {
        try {
            val stat = StatFs(Environment.getExternalStorageDirectory().path)
            val totalBytes = stat.totalBytes
            val freeBytes = stat.availableBytes
            val usedBytes = totalBytes - freeBytes
            val usagePercentage = if (totalBytes > 0) {
                (usedBytes.toFloat() / totalBytes.toFloat()) * 100f
            } else {
                0f
            }
            
            emit(
                StorageInfo(
                    totalSpace = totalBytes,
                    usedSpace = usedBytes,
                    freeSpace = freeBytes,
                    usagePercentage = usagePercentage
                )
            )
        } catch (e: Exception) {
            // In case of error, emit empty storage info
            emit(StorageInfo.EMPTY)
        }
    }
}
