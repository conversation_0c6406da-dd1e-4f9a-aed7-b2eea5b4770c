package com.example.clean0522.data.provider

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import com.example.clean0522.domain.model.MediaFile
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Provider for accessing media files through Android MediaStore
 */
class MediaProvider(private val context: Context) {

    private val contentResolver: ContentResolver = context.contentResolver

    /**
     * Get all images from MediaStore
     */
    suspend fun getImages(): List<MediaFile> {
        val images = mutableListOf<MediaFile>()
        val projection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.DATA,
            MediaStore.Images.Media.SIZE,
            MediaStore.Images.Media.DATE_MODIFIED,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.DATE_TAKEN,
            MediaStore.Images.Media.MIME_TYPE,
            MediaStore.Images.Media.WIDTH,
            MediaStore.Images.Media.HEIGHT
        )

        val cursor = contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            "${MediaStore.Images.Media.DATE_TAKEN} DESC"
        )

        cursor?.use {
            while (it.moveToNext()) {
                val mediaFile = createMediaFileFromCursor(it, projection)
                if (mediaFile.file.exists()) {
                    images.add(mediaFile)
                }
            }
        }

        return images
    }

    /**
     * Get all videos from MediaStore
     */
    suspend fun getVideos(): List<MediaFile> {
        val videos = mutableListOf<MediaFile>()
        val projection = arrayOf(
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.DISPLAY_NAME,
            MediaStore.Video.Media.DATA,
            MediaStore.Video.Media.SIZE,
            MediaStore.Video.Media.DATE_MODIFIED,
            MediaStore.Video.Media.DATE_ADDED,
            MediaStore.Video.Media.DATE_TAKEN,
            MediaStore.Video.Media.MIME_TYPE,
            MediaStore.Video.Media.DURATION,
            MediaStore.Video.Media.WIDTH,
            MediaStore.Video.Media.HEIGHT
        )

        val cursor = contentResolver.query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            "${MediaStore.Video.Media.DATE_TAKEN} DESC"
        )

        cursor?.use {
            while (it.moveToNext()) {
                val mediaFile = createVideoFileFromCursor(it, projection)
                if (mediaFile.file.exists()) {
                    videos.add(mediaFile)
                }
            }
        }

        return videos
    }

    /**
     * Get all audio files from MediaStore
     */
    suspend fun getAudios(): List<MediaFile> {
        val audios = mutableListOf<MediaFile>()
        val projection = arrayOf(
            MediaStore.Audio.Media._ID,
            MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.SIZE,
            MediaStore.Audio.Media.DATE_MODIFIED,
            MediaStore.Audio.Media.DATE_ADDED,
            MediaStore.Audio.Media.MIME_TYPE,
            MediaStore.Audio.Media.DURATION
        )

        val cursor = contentResolver.query(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            projection,
            "${MediaStore.Audio.Media.IS_MUSIC} = 1",
            null,
            "${MediaStore.Audio.Media.DATE_ADDED} DESC"
        )

        cursor?.use {
            while (it.moveToNext()) {
                val mediaFile = createAudioFileFromCursor(it, projection)
                if (mediaFile.file.exists()) {
                    audios.add(mediaFile)
                }
            }
        }

        return audios
    }

    /**
     * Get all documents from file system
     */
    suspend fun getDocuments(): List<MediaFile> {
        return getFilesByExtensions(listOf("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"))
    }

    /**
     * Get all archive files from file system
     */
    suspend fun getArchives(): List<MediaFile> {
        return getFilesByExtensions(listOf("zip", "rar", "7z", "tar", "gz"))
    }

    /**
     * Get all APK files from file system
     */
    suspend fun getApks(): List<MediaFile> {
        return getFilesByExtensions(listOf("apk"))
    }

    /**
     * Get files by extensions from file system
     */
    private suspend fun getFilesByExtensions(extensions: List<String>): List<MediaFile> {
        val files = mutableListOf<MediaFile>()
        val projection = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.DATE_ADDED,
            MediaStore.Files.FileColumns.MIME_TYPE
        )

        val selection = extensions.joinToString(" OR ") {
            "${MediaStore.Files.FileColumns.DATA} LIKE '%.${it}'"
        }

        val cursor = contentResolver.query(
            MediaStore.Files.getContentUri("external"),
            projection,
            selection,
            null,
            "${MediaStore.Files.FileColumns.DATE_ADDED} DESC"
        )

        cursor?.use {
            while (it.moveToNext()) {
                val mediaFile = createFileFromCursor(it, projection)
                if (mediaFile.file.exists()) {
                    files.add(mediaFile)
                }
            }
        }

        return files
    }

    private fun createFileFromCursor(cursor: Cursor, projection: Array<String>): MediaFile {
        val dataIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA)
        val nameIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME)
        val sizeIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.SIZE)
        val dateModifiedIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
        val dateAddedIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_ADDED)
        val mimeTypeIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MIME_TYPE)

        val path = cursor.getString(dataIndex)
        val file = File(path)

        return MediaFile(
            file = file,
            name = cursor.getString(nameIndex),
            path = path,
            size = cursor.getLong(sizeIndex),
            lastModified = cursor.getLong(dateModifiedIndex) * 1000,
            dateAdded = cursor.getLong(dateAddedIndex) * 1000,
            mimeType = cursor.getString(mimeTypeIndex) ?: ""
        )
    }

    private fun createMediaFileFromCursor(cursor: Cursor, projection: Array<String>): MediaFile {
        val dataIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
        val nameIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
        val sizeIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
        val dateModifiedIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED)
        val dateAddedIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
        val dateTakenIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_TAKEN)
        val mimeTypeIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE)
        val widthIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.WIDTH)
        val heightIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.HEIGHT)

        val path = cursor.getString(dataIndex)
        val file = File(path)

        return MediaFile(
            file = file,
            name = cursor.getString(nameIndex),
            path = path,
            size = cursor.getLong(sizeIndex),
            lastModified = cursor.getLong(dateModifiedIndex) * 1000,
            dateAdded = cursor.getLong(dateAddedIndex) * 1000,
            dateTaken = cursor.getLong(dateTakenIndex),
            mimeType = cursor.getString(mimeTypeIndex) ?: "",
            width = cursor.getInt(widthIndex),
            height = cursor.getInt(heightIndex)
        )
    }

    private fun createVideoFileFromCursor(cursor: Cursor, projection: Array<String>): MediaFile {
        val dataIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
        val nameIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME)
        val sizeIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE)
        val dateModifiedIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_MODIFIED)
        val dateAddedIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED)
        val dateTakenIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_TAKEN)
        val mimeTypeIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.MIME_TYPE)
        val durationIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION)
        val widthIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.WIDTH)
        val heightIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.HEIGHT)

        val path = cursor.getString(dataIndex)
        val file = File(path)

        return MediaFile(
            file = file,
            name = cursor.getString(nameIndex),
            path = path,
            size = cursor.getLong(sizeIndex),
            lastModified = cursor.getLong(dateModifiedIndex) * 1000,
            dateAdded = cursor.getLong(dateAddedIndex) * 1000,
            dateTaken = cursor.getLong(dateTakenIndex),
            mimeType = cursor.getString(mimeTypeIndex) ?: "",
            duration = cursor.getLong(durationIndex),
            width = cursor.getInt(widthIndex),
            height = cursor.getInt(heightIndex)
        )
    }

    private fun createAudioFileFromCursor(cursor: Cursor, projection: Array<String>): MediaFile {
        val dataIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
        val nameIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME)
        val sizeIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)
        val dateModifiedIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED)
        val dateAddedIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_ADDED)
        val mimeTypeIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE)
        val durationIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)

        val path = cursor.getString(dataIndex)
        val file = File(path)

        return MediaFile(
            file = file,
            name = cursor.getString(nameIndex),
            path = path,
            size = cursor.getLong(sizeIndex),
            lastModified = cursor.getLong(dateModifiedIndex) * 1000,
            dateAdded = cursor.getLong(dateAddedIndex) * 1000,
            mimeType = cursor.getString(mimeTypeIndex) ?: "",
            duration = cursor.getLong(durationIndex)
        )
    }
}
