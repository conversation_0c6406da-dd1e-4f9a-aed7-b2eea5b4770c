package com.example.clean0522.data.antivirus

import android.content.Context
import com.example.clean0522.domain.model.ThreatInfo
import com.example.clean0522.utils.logD
import com.example.clean0522.utils.logE
import com.trustlook.sdk.cloudscan.CloudScanClient
import com.trustlook.sdk.cloudscan.CloudScanListener
import com.trustlook.sdk.data.AppInfo
import com.trustlook.sdk.data.Region
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow

/**
 * Manager for CloudScan SDK operations
 * This class wraps the Trustlook CloudScan SDK functionality
 */
class CloudScanManager(private val context: Context) {

    companion object {
        private const val TAG = "CloudScanManager"
        private const val CONNECTION_TIMEOUT = 30000
        private const val SOCKET_TIMEOUT = 30000
    }

    // TODO: Uncomment when SDK is available
     private var cloudScanClient: CloudScanClient? = null

    /**
     * Initialize the CloudScan client
     */
    fun initialize(): Boolean {
        return try {
            cloudScanClient = CloudScanClient.Builder(context)
                .setRegion(Region.INTL) // or Region.BAIDU for China
                .setConnectionTimeout(CONNECTION_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build()
            logD(TAG, "CloudScan client initialized successfully")
            true
        } catch (e: Exception) {
            logE(TAG, "Failed to initialize CloudScan client", e)
            false
        }
    }

    /**
     * Start quick scan
     */
    fun startQuickScan(): Flow<ScanProgress> = callbackFlow {
        try {
            cloudScanClient?.startQuickScan(object : CloudScanListener() {
                override fun onScanStarted() {
                    trySend(ScanProgress.Started)
                }

                override fun onScanProgress(curr: Int, total: Int, result: AppInfo?) {
                    val progress = if (total > 0) (curr * 100) / total else 0
                    trySend(ScanProgress.Progress(curr, total, progress, result?.let { convertAppInfo(it) }))
                }

                override fun onScanError(errCode: Int, errMessage: String?) {
                    trySend(ScanProgress.Error(errCode, errMessage ?: "Unknown error"))
                }

                override fun onScanCanceled() {
                    trySend(ScanProgress.Cancelled)
                }

                override fun onScanInterrupt() {
                    trySend(ScanProgress.Interrupted)
                }

                override fun onScanFinished(results: List<AppInfo>?) {
                    val threats = results?.mapNotNull { convertAppInfo(it) } ?: emptyList()
                    trySend(ScanProgress.Completed(threats))
                }
            })

        } catch (e: Exception) {
            trySend(ScanProgress.Error(-1, e.message ?: "Unknown error"))
        }

        awaitClose {
            logD(TAG, "Quick scan flow closed")
        }
    }

    /**
     * Start folder scan
     */
    fun startFolderScan(folderPath: String): Flow<ScanProgress> = callbackFlow {
        try {
            cloudScanClient?.startFolderScan(listOf(folderPath), object : CloudScanListener() {
                override fun onScanStarted() {
                    trySend(ScanProgress.Started)
                }

                override fun onScanProgress(curr: Int, total: Int, result: AppInfo?) {
                    val progress = if (total > 0) (curr * 100) / total else 0
                    trySend(ScanProgress.Progress(curr, total, progress, result?.let { convertAppInfo(it) }))
                }

                override fun onScanError(errCode: Int, errMessage: String?) {
                    trySend(ScanProgress.Error(errCode, errMessage ?: "Unknown error"))
                }

                override fun onScanCanceled() {
                    trySend(ScanProgress.Cancelled)
                }

                override fun onScanInterrupt() {
                    trySend(ScanProgress.Interrupted)
                }

                override fun onScanFinished(results: List<AppInfo>?) {
                    val threats = results?.mapNotNull { convertAppInfo(it) } ?: emptyList()
                    trySend(ScanProgress.Completed(threats))
                }
            })
        } catch (e: Exception) {
            trySend(ScanProgress.Error(-1, e.message ?: "Unknown error"))
        }

        awaitClose {
            logD(TAG, "Folder scan flow closed")
        }
    }

    /**
     * Start comprehensive scan
     */
    fun startComprehensiveScan(): Flow<ScanProgress> = callbackFlow {
        try {
            cloudScanClient?.startComprehensiveScan(object : CloudScanListener() {
                override fun onScanStarted() {
                    trySend(ScanProgress.Started)
                }

                override fun onScanProgress(curr: Int, total: Int, result: AppInfo?) {
                    val progress = if (total > 0) (curr * 100) / total else 0
                    trySend(ScanProgress.Progress(curr, total, progress, result?.let { convertAppInfo(it) }))
                }

                override fun onScanError(errCode: Int, errMessage: String?) {
                    trySend(ScanProgress.Error(errCode, errMessage ?: "Unknown error"))
                }

                override fun onScanCanceled() {
                    trySend(ScanProgress.Cancelled)
                }

                override fun onScanInterrupt() {
                    trySend(ScanProgress.Interrupted)
                }

                override fun onScanFinished(results: List<AppInfo>?) {
                    val threats = results?.mapNotNull { convertAppInfo(it) } ?: emptyList()
                    results?.forEach { logD(TAG, "onScanFinished: ${it.appName} ${it.score} ${it.apkPath} ${it.virusName}") }
                    trySend(ScanProgress.Completed(threats))
                }
            })
        } catch (e: Exception) {
            trySend(ScanProgress.Error(-1, e.message ?: "Unknown error"))
        }

        awaitClose {
            logD(TAG, "Comprehensive scan flow closed")
        }
    }

    /**
     * Cancel current scan
     */
    fun cancelScan() {
        cloudScanClient?.cancelScan()
        logD(TAG, "Scan cancelled")
    }

    /**
     * Convert SDK AppInfo to our ThreatInfo model
     */
    private fun convertAppInfo(appInfo: AppInfo): ThreatInfo? {
        return try {
            if (appInfo.score >= 6) {
                ThreatInfo(
                    id = appInfo.packageName ?: appInfo.apkPath ?: appInfo.md5,
                    appName = appInfo.appName ?: "Unknown",
                    packageName = appInfo.packageName,
                    filePath = appInfo.apkPath,
                    md5 = appInfo.md5 ?: "",
                    score = appInfo.score,
                    virusName = appInfo.virusName ?: "",
                    category = appInfo.category[0]?: "",
                    summary = appInfo.summary ?: emptyArray(),
                    isFromStatic = appInfo.isFromStatic
                )
            } else {
                null
            }
        } catch (e: Exception) {
            logE(TAG, "Error converting AppInfo", e)
            null
        }
    }
}

/**
 * Sealed class representing scan progress states
 */
sealed class ScanProgress {
    object Started : ScanProgress()
    data class Progress(
        val current: Int,
        val total: Int,
        val percentage: Int,
        val currentThreat: ThreatInfo?
    ) : ScanProgress()
    data class Completed(val threats: List<ThreatInfo>) : ScanProgress()
    data class Error(val errorCode: Int, val errorMessage: String) : ScanProgress()
    object Cancelled : ScanProgress()
    object Interrupted : ScanProgress()
}
