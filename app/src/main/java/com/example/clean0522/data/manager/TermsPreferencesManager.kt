package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV

/**
 * Manager for terms and privacy agreement preferences using MMKV
 */
class TermsPreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_TERMS_ACCEPTED = "terms_accepted"
        
        @Volatile
        private var INSTANCE: TermsPreferencesManager? = null
        
        fun getInstance(): TermsPreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TermsPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Set terms and privacy agreement accepted state
     */
    fun setTermsAccepted(accepted: Boolean) {
        mmkv.putBoolean(KEY_TERMS_ACCEPTED, accepted)
    }
    
    /**
     * Get terms and privacy agreement accepted state
     * Default is false (not accepted)
     */
    fun isTermsAccepted(): Boolean {
        return mmkv.getBoolean(KEY_TERMS_ACCEPTED, false)
    }
    
    /**
     * Clear all terms preferences
     */
    fun clearPreferences() {
        mmkv.remove(KEY_TERMS_ACCEPTED)
    }
}
