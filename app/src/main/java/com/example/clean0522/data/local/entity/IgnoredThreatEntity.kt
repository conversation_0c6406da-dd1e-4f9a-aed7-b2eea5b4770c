package com.example.clean0522.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Entity for storing ignored threats in the database
 */
@Entity(tableName = "ignored_threats")
data class IgnoredThreatEntity(
    @PrimaryKey
    val id: String, // Use package name or file path as unique identifier
    val appName: String,
    val packageName: String?,
    val filePath: String?,
    val md5: String,
    val virusName: String,
    val category: String,
    val score: Int,
    val ignoredAt: Long = System.currentTimeMillis()
)
