package com.example.clean0522.data.local.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.example.clean0522.data.local.dao.IgnoredThreatDao
import com.example.clean0522.data.local.entity.IgnoredThreatEntity

/**
 * Main database for the application
 */
@Database(
    entities = [IgnoredThreatEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun ignoredThreatDao(): IgnoredThreatDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getInstance(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "clean_app_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
