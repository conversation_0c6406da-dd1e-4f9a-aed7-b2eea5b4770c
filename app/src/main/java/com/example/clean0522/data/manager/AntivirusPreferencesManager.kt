package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV

/**
 * Manager for antivirus-related preferences using MMKV
 */
class AntivirusPreferencesManager {
    
    companion object {
        private const val MMKV_ID = "antivirus_preferences"
        private const val KEY_PRIVACY_AGREEMENT_ACCEPTED = "privacy_agreement_accepted"
        
        @Volatile
        private var INSTANCE: AntivirusPreferencesManager? = null
        
        fun getInstance(): AntivirusPreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AntivirusPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID(MMKV_ID)
    }
    
    /**
     * Check if user has accepted the privacy agreement
     */
    fun isPrivacyAgreementAccepted(): Boolean {
        return mmkv.decodeBool(KEY_PRIVACY_AGREEMENT_ACCEPTED, false)
    }
    
    /**
     * Set privacy agreement acceptance status
     */
    fun setPrivacyAgreementAccepted(accepted: Boolean) {
        mmkv.encode(KEY_PRIVACY_AGREEMENT_ACCEPTED, accepted)
    }
}
