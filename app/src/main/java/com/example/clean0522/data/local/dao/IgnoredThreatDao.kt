package com.example.clean0522.data.local.dao

import androidx.room.*
import com.example.clean0522.data.local.entity.IgnoredThreatEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for ignored threats
 */
@Dao
interface IgnoredThreatDao {
    
    @Query("SELECT * FROM ignored_threats")
    fun getAllIgnoredThreats(): Flow<List<IgnoredThreatEntity>>
    
    @Query("SELECT * FROM ignored_threats WHERE id = :id")
    suspend fun getIgnoredThreatById(id: String): IgnoredThreatEntity?
    
    @Query("SELECT COUNT(*) FROM ignored_threats WHERE id = :id")
    suspend fun isIgnored(id: String): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIgnoredThreat(threat: IgnoredThreatEntity)
    
    @Delete
    suspend fun deleteIgnoredThreat(threat: IgnoredThreatEntity)
    
    @Query("DELETE FROM ignored_threats WHERE id = :id")
    suspend fun deleteIgnoredThreatById(id: String)
    
    @Query("DELETE FROM ignored_threats")
    suspend fun clearAllIgnoredThreats()
}
