package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV

/**
 * Manager for notification preferences using MMKV
 */
class NotificationPreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
        
        @Volatile
        private var INSTANCE: NotificationPreferencesManager? = null
        
        fun getInstance(): NotificationPreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotificationPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Set notification enabled state in MMKV
     * This is the user preference, separate from system permission
     */
    fun setNotificationEnabled(enabled: Boolean) {
        mmkv.putBoolean(KEY_NOTIFICATION_ENABLED, enabled)
    }
    
    /**
     * Get notification enabled state from MMKV
     * Default is true (enabled)
     */
    fun isNotificationEnabled(): Boolean {
        return mmkv.getBoolean(KEY_NOTIFICATION_ENABLED, true)
    }
    
    /**
     * Clear all notification preferences
     */
    fun clearPreferences() {
        mmkv.remove(KEY_NOTIFICATION_ENABLED)
    }
}
