package com.example.clean0522

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.ui.components.SortDialog
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.AppManagerUtils
import com.example.clean0522.viewmodel.AppManagerViewModel

/**
 * App type enumeration for filtering
 */
enum class AppType(val displayName: String) {
    ALL("All"),
    SYSTEM("System"),
    INSTALLED("Installed")
}

/**
 * Activity for app management tools
 */
class AppInfoActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_APP_TYPE = "app_type"
        const val TYPE_APP_MANAGER = "app_manager"
    }

    @Composable
    override fun setHomePage() {
        val appType = intent.getStringExtra(EXTRA_APP_TYPE) ?: TYPE_APP_MANAGER
        Clean0522Theme {
            AppInfoScreen(
                appType = appType,
                onBackClick = { finish() }
            )
        }
    }
}

/**
 * App info screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppInfoScreen(
    appType: String,
    onBackClick: () -> Unit
) {
    val title = when (appType) {
        AppInfoActivity.TYPE_APP_MANAGER -> stringResource(R.string.tool_app_manager)
        else -> stringResource(R.string.app_manager_title)
    }

    val viewModel: AppManagerViewModel = viewModel()
    val showSortDialog by viewModel.showSortDialog.collectAsState()
    val currentSortOption by viewModel.currentSortOption.collectAsState()

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            TopNavBar(
                title = title,
                showBackButton = true,
                backButtonAction = onBackClick,
                settingsButtonContent = {
                    Image(painter = painterResource(R.mipmap.ic_sort),
                        contentDescription = stringResource(R.string.sort),
                        modifier = Modifier.size(24.dp)
                            .clickable {
                                viewModel.showSortDialog()
                            }
                    )
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (appType) {
                AppInfoActivity.TYPE_APP_MANAGER -> AppManagerContent(viewModel = viewModel)
                else -> PlaceholderContent(title)
            }
        }
    }

    // Sort dialog
    if (showSortDialog) {
        SortDialog(
            currentSortOption = currentSortOption,
            onSortOptionSelected = { sortOption ->
                viewModel.updateSortOption(sortOption)
            },
            onDismiss = { viewModel.hideSortDialog() }
        )
    }
}

/**
 * App manager content with tabs and app list
 */
@Composable
fun AppManagerContent(viewModel: AppManagerViewModel) {
    val context = LocalContext.current
    val isLoading by viewModel.isLoading.collectAsState()
    val allApps by viewModel.allApps.collectAsState()
    val systemApps by viewModel.systemApps.collectAsState()
    val installedApps by viewModel.installedApps.collectAsState()
    val allAppsCount by viewModel.allAppsCount.collectAsState()
    val systemAppsCount by viewModel.systemAppsCount.collectAsState()
    val installedAppsCount by viewModel.installedAppsCount.collectAsState()

    var selectedAppType by remember { mutableStateOf(AppType.ALL) }

    // Load apps when first composed
    LaunchedEffect(Unit) {
        viewModel.loadApps(context)
    }

    Column(
        modifier = Modifier.fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        // App type filter
        AppTypeFilter(
            selectedType = selectedAppType,
            onTypeSelected = { selectedAppType = it }
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Content based on selected tab
        when (selectedAppType) {
            AppType.ALL -> AppListContent(
                apps = allApps,
                isLoading = isLoading,
                appsCount = allAppsCount,
                onAppClick = { app ->
                    val intent = AppDetailActivity.createIntent(context, app.packageName, app.appName)
                    context.startActivity(intent)
                }
            )
            AppType.SYSTEM -> AppListContent(
                apps = systemApps,
                isLoading = isLoading,
                appsCount = systemAppsCount,
                onAppClick = { app ->
                    val intent = AppDetailActivity.createIntent(context, app.packageName, app.appName)
                    context.startActivity(intent)
                }
            )
            AppType.INSTALLED -> AppListContent(
                apps = installedApps,
                isLoading = isLoading,
                appsCount = installedAppsCount,
                onAppClick = { app ->
                    val intent = AppDetailActivity.createIntent(context, app.packageName, app.appName)
                    context.startActivity(intent)
                }
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
    }
}

/**
 * App type filter component
 */
@Composable
fun AppTypeFilter(
    selectedType: AppType,
    onTypeSelected: (AppType) -> Unit,
    modifier: Modifier = Modifier
) {
    val appTypes = AppType.values()

    Column {
        Row(
            modifier = modifier
                .fillMaxWidth(),
        ) {
            appTypes.forEach { type ->
                AppTypeChip(
                    type = type,
                    isSelected = type == selectedType,
                    onClick = { onTypeSelected(type) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
        Spacer(modifier = Modifier.height(1.dp)
            .fillMaxWidth()
            .background(colorResource(R.color.text_gray)))
    }
}

/**
 * App type chip component
 */
@Composable
fun AppTypeChip(
    type: AppType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                if (isSelected) {
                    colorResource(R.color.text_blue)
                } else {
                    Color.Transparent
                }
                , shape = RoundedCornerShape(topStart = 7.dp, topEnd = 7.dp)
            )
            .clickable { onClick() }
            .padding(vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = type.displayName,
            fontWeight = FontWeight.SemiBold,
            color = if (isSelected) {
                Color.White
            } else {
                Color(0xFFA3A3A3)
            },
            fontSize = 12.sp
        )
    }
}

/**
 * App list content composable
 */
@Composable
fun AppListContent(
    apps: List<AppManagerUtils.AppInfo>,
    isLoading: Boolean,
    appsCount: Int,
    onAppClick: (AppManagerUtils.AppInfo) -> Unit
) {
    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else if (apps.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_apps_found),
                fontSize = 16.sp,
                color = colorResource(R.color.text_black)
            )
        }
    } else {
        Card(
            modifier = Modifier
                .fillMaxSize(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(12.dp)
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(apps) { app ->
                    AppItem(
                        app = app,
                        onClick = { onAppClick(app) }
                    )
                    Divider(
                        modifier = Modifier,
                        thickness = 0.5.dp
                    )
                }
            }
        }
    }
}

/**
 * Individual app item composable
 */
@Composable
fun AppItem(
    app: AppManagerUtils.AppInfo,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // App icon
        if (app.icon != null) {
            Image(
                bitmap = app.icon.toBitmap(64, 64).asImageBitmap(),
                contentDescription = app.appName,
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(8.dp))
            )
        } else {
            Image(
                painter = painterResource(R.mipmap.ic_apk),
                contentDescription = app.appName,
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(8.dp))
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        // App info
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = app.appName,
                fontSize = 13.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                color = colorResource(R.color.text_black),
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = app.installDate,
                fontSize = 12.sp,
                color = Color(0xFFA3A3A3),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        // More button

        Text(
            text = app.sizeFormatted,
            fontSize = 12.sp,
            color = Color(0xFFA3A3A3),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        Image(painter = painterResource(R.mipmap.iin_more),
            contentDescription = "More",
            modifier = Modifier
                .size(24.dp)
                .clickable { onClick() }
        )
    }
}

/**
 * Placeholder content for unknown app types
 */
@Composable
fun PlaceholderContent(title: String) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = title,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = stringResource(R.string.coming_soon),
            fontSize = 16.sp
        )
    }
}
