package com.example.clean0522

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.AppDetailUtils
import com.example.clean0522.viewmodel.AppDetailViewModel

/**
 * Activity for displaying detailed app information
 */
class AppDetailActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_PACKAGE_NAME = "package_name"
        const val EXTRA_APP_NAME = "app_name"

        fun createIntent(context: Context, packageName: String, appName: String): Intent {
            return Intent(context, AppDetailActivity::class.java).apply {
                putExtra(EXTRA_PACKAGE_NAME, packageName)
                putExtra(EXTRA_APP_NAME, appName)
            }
        }
    }

    @Composable
    override fun setHomePage() {
        val packageName = intent.getStringExtra(EXTRA_PACKAGE_NAME) ?: ""
        val appName = intent.getStringExtra(EXTRA_APP_NAME) ?: ""

        Clean0522Theme {
            AppDetailScreen(
                packageName = packageName,
                appName = appName,
                onBackClick = { finish() }
            )
        }
    }
}

/**
 * App detail screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDetailScreen(
    packageName: String,
    appName: String,
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val viewModel: AppDetailViewModel = viewModel()
    val appDetailInfo by viewModel.appDetailInfo.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    var selectedTabIndex by remember { mutableStateOf(0) }

    // Load app detail when first composed
    LaunchedEffect(packageName) {
        if (packageName.isNotEmpty()) {
            viewModel.loadAppDetail(context, packageName)
        }
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            TopNavBar(
                title = appName.ifEmpty { stringResource(R.string.app_details_title) },
                showBackButton = true,
                backButtonAction = onBackClick,
                settingsButtonContent = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (error != null) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = error!!,
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = {
                                viewModel.clearError()
                                viewModel.loadAppDetail(context, packageName)
                            }
                        ) {
                            Text(stringResource(R.string.retry))
                        }
                    }
                }
            } else if (appDetailInfo != null) {
                // Tab Row
                TabRow(
                    selectedTabIndex = selectedTabIndex,
                    modifier = Modifier.fillMaxWidth(),
                    containerColor = MaterialTheme.colorScheme.surface
                ) {
                    Tab(
                        selected = selectedTabIndex == 0,
                        onClick = { selectedTabIndex = 0 },
                        text = {
                            Text(
                                text = stringResource(R.string.tab_general),
                                fontSize = 16.sp,
                                fontWeight = if (selectedTabIndex == 0) FontWeight.Bold else FontWeight.Normal
                            )
                        }
                    )
                    Tab(
                        selected = selectedTabIndex == 1,
                        onClick = { selectedTabIndex = 1 },
                        text = {
                            Text(
                                text = stringResource(R.string.tab_permission),
                                fontSize = 16.sp,
                                fontWeight = if (selectedTabIndex == 1) FontWeight.Bold else FontWeight.Normal
                            )
                        }
                    )
                    Tab(
                        selected = selectedTabIndex == 2,
                        onClick = { selectedTabIndex = 2 },
                        text = {
                            Text(
                                text = stringResource(R.string.tab_certificates),
                                fontSize = 16.sp,
                                fontWeight = if (selectedTabIndex == 2) FontWeight.Bold else FontWeight.Normal
                            )
                        }
                    )
                }

                // Content based on selected tab
                when (selectedTabIndex) {
                    0 -> GeneralTabContent(appDetailInfo!!)
                    1 -> PermissionTabContent(appDetailInfo!!.permissions)
                    2 -> CertificatesTabContent(appDetailInfo!!.certificates)
                }
            }
        }
    }
}

/**
 * General tab content showing app basic information
 */
@Composable
fun GeneralTabContent(appDetailInfo: AppDetailUtils.AppDetailInfo) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // App header with icon and name
        item {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (appDetailInfo.icon != null) {
                    Image(
                        bitmap = appDetailInfo.icon.toBitmap(64, 64).asImageBitmap(),
                        contentDescription = appDetailInfo.appName,
                        modifier = Modifier
                            .size(64.dp)
                            .clip(RoundedCornerShape(12.dp))
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .size(64.dp)
                            .background(
                                color = MaterialTheme.colorScheme.surfaceVariant,
                                shape = RoundedCornerShape(12.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = appDetailInfo.appName.take(1).uppercase(),
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = appDetailInfo.appName,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
            }
        }

        // App information items
        item {
            AppInfoItem(
                label = stringResource(R.string.apk_size),
                value = AppDetailUtils.formatFileSize(appDetailInfo.apkSize)
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.application_name),
                value = appDetailInfo.appName
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.min_sdk),
                value = appDetailInfo.minSdkVersion.toString()
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.min_sdk_version),
                value = appDetailInfo.minSdkVersion.toString()
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.package_name),
                value = appDetailInfo.packageName
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.process_name),
                value = appDetailInfo.processName
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.system_application),
                value = stringResource(if (appDetailInfo.isSystemApp) R.string.yes else R.string.no)
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.target_sdk),
                value = appDetailInfo.targetSdkVersion.toString()
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.target_sdk_version),
                value = appDetailInfo.targetSdkVersion.toString()
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.version_code),
                value = appDetailInfo.versionCode.toString()
            )
        }
        item {
            AppInfoItem(
                label = stringResource(R.string.version_name),
                value = appDetailInfo.versionName
            )
        }
    }
}

/**
 * Individual app info item
 */
@Composable
fun AppInfoItem(label: String, value: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
            Text(
                text = value,
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

/**
 * Permission tab content showing app permissions
 */
@Composable
fun PermissionTabContent(permissions: List<AppDetailUtils.AppPermissionInfo>) {
    if (permissions.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_permissions_found),
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(permissions) { permission ->
                PermissionItem(permission = permission)
            }
        }
    }
}

/**
 * Individual permission item
 */
@Composable
fun PermissionItem(permission: AppDetailUtils.AppPermissionInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Permission display name (uppercase, formatted)
            Text(
                text = permission.name.substringAfterLast(".").replace("_", " "),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Protection level
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.protection_level),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = permission.protectionLevel,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.End,
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // Constant value (full permission name)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.constant_value),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = permission.name,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.End,
                    modifier = Modifier.weight(1f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // Grant status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.grant_status),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f)
                )
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = if (permission.isGranted)
                            MaterialTheme.colorScheme.primaryContainer
                        else
                            MaterialTheme.colorScheme.errorContainer
                    ),
                    shape = RoundedCornerShape(4.dp),
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Text(
                        text = stringResource(if (permission.isGranted) R.string.permission_granted else R.string.permission_denied),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (permission.isGranted)
                            MaterialTheme.colorScheme.onPrimaryContainer
                        else
                            MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            // Description
            if (permission.description != permission.name && permission.description.isNotBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = permission.description,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    lineHeight = 20.sp
                )
            }
        }
    }
}

/**
 * Certificates tab content showing app certificates
 */
@Composable
fun CertificatesTabContent(certificates: List<AppDetailUtils.CertificateInfo>) {
    if (certificates.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_certificates_found),
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(certificates) { certificate ->
                CertificateItem(certificate = certificate)
            }
        }
    }
}

/**
 * Individual certificate item
 */
@Composable
fun CertificateItem(certificate: AppDetailUtils.CertificateInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Sign algorithm
            CertificateInfoRow(
                label = stringResource(R.string.sign_algorithm),
                value = certificate.signatureAlgorithm
            )

            // Valid from
            CertificateInfoRow(
                label = stringResource(R.string.valid_from),
                value = certificate.validFrom
            )

            // Valid to
            CertificateInfoRow(
                label = stringResource(R.string.valid_to),
                value = certificate.validTo
            )

            // Public key MD5
            CertificateInfoRow(
                label = stringResource(R.string.public_key_md5),
                value = certificate.publicKeyMD5
            )

            // Certificate MD5
            CertificateInfoRow(
                label = stringResource(R.string.certificate_md5),
                value = certificate.certificateMD5
            )

            // Serial number
            CertificateInfoRow(
                label = stringResource(R.string.serial_number),
                value = certificate.serialNumber
            )

            // Issuer name
            CertificateInfoRow(
                label = stringResource(R.string.issuer_name),
                value = certificate.issuerName
            )

            // Issuer organization
            CertificateInfoRow(
                label = stringResource(R.string.issuer_organization),
                value = certificate.issuerOrganization
            )

            // Issuer country
            CertificateInfoRow(
                label = stringResource(R.string.issuer_country),
                value = certificate.issuerCountry
            )

            // Subject name
            CertificateInfoRow(
                label = stringResource(R.string.subject_name),
                value = certificate.subjectName
            )

            // Subject organization
            CertificateInfoRow(
                label = stringResource(R.string.subject_organization),
                value = certificate.subjectOrganization
            )

            // Subject country
            CertificateInfoRow(
                label = stringResource(R.string.subject_country),
                value = certificate.subjectCountry
            )
        }
    }
}

/**
 * Certificate information row
 */
@Composable
fun CertificateInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(1f),
            maxLines = 3,
            overflow = TextOverflow.Ellipsis
        )
    }
}
