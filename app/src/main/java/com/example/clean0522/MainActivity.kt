package com.example.clean0522

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.clean0522.data.manager.AntivirusPreferencesManager
import com.example.clean0522.data.repository.StorageRepositoryImpl
import com.example.clean0522.domain.usecase.GetStorageInfoUseCase
import com.example.clean0522.ui.components.AntivirusPrivacyDialog
import com.example.clean0522.ui.features.files.FilesViewModel
import com.example.clean0522.ui.navigation.BaseHomeNavigation
import com.example.clean0522.ui.navigation.BottomNavBar
import com.example.clean0522.ui.navigation.Screen
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme

class MainActivity : ComponentBaseActivity() {

    @Composable
    override fun setHomePage() {
        val storageRepository = StorageRepositoryImpl(this)
        val getStorageInfoUseCase = GetStorageInfoUseCase(storageRepository)
        Clean0522Theme {
            val filesViewModel = FilesViewModel(getStorageInfoUseCase)
            MainScreen(
                filesViewModel = filesViewModel,
                activity = this@MainActivity
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    filesViewModel: FilesViewModel,
    activity: ComponentBaseActivity
) {
    val context = LocalContext.current
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    // Antivirus privacy dialog state
    var showAntivirusPrivacyDialog by remember { mutableStateOf(false) }
    var pendingScanType by remember { mutableStateOf<String?>(null) }
    val antivirusPreferencesManager = remember { AntivirusPreferencesManager.getInstance() }

    val showBottomBar = currentRoute in listOf(
        Screen.Files.route,
        Screen.Antivirus.route,
        Screen.More.route
    )

    val title = when (currentRoute) {
        Screen.Files.route -> stringResource(R.string.nav_files)
        Screen.Antivirus.route -> stringResource(R.string.nav_antivirus)
        Screen.More.route -> stringResource(R.string.nav_more)
        Screen.Settings.route -> stringResource(R.string.nav_settings)
        else -> stringResource(R.string.app_name)
    }

    val showBackButton = currentRoute == Screen.Settings.route

    val showSettingsButton = currentRoute in listOf(
        Screen.Files.route,
        Screen.Antivirus.route,
        Screen.More.route
    )

    Scaffold(
        containerColor = colorResource(R.color.bg_color),
        modifier = Modifier.navigationBarsPadding(),
        topBar = {
            TopNavBar(
                title = title,
                showBackButton = showBackButton,
                backButtonAction = { navController.popBackStack() },
                settingsButtonContent = {
                    if (showSettingsButton) {
                        Image(
                            painter = painterResource(if (currentRoute == Screen.Antivirus.route){
                                R.mipmap.ic_info
                            }else{
                                R.mipmap.home_set
                            }),
                            contentDescription = stringResource(R.string.settings),
                            modifier = Modifier.size(28.dp)
                                .clickable {
                                    if (currentRoute == Screen.Antivirus.route) {
                                        showAntivirusPrivacyDialog = true
                                    } else {
                                        navController.navigate(Screen.Settings.route)
                                    }
                                }
                        )
                    }
                }
            )
        },
        bottomBar = {
            if (showBottomBar) {
                Card(elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                    shape = RectangleShape
                ) {
                    BottomNavBar(navController = navController)
                }
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            BaseHomeNavigation(
                navController = navController,
                filesViewModel = filesViewModel,
                activity = activity,
                onRequestAntivirusPrivacyDialog = { scanType ->
                    pendingScanType = scanType
                    showAntivirusPrivacyDialog = true
                }
            )
        }
    }

    // Antivirus privacy dialog
    if (showAntivirusPrivacyDialog) {
        AntivirusPrivacyDialog(
            onDismiss = {
                showAntivirusPrivacyDialog = false
                pendingScanType = null
            },
            onConfirm = {
                antivirusPreferencesManager.setPrivacyAgreementAccepted(true)
                showAntivirusPrivacyDialog = false

                pendingScanType?.let { scanType ->
                    // After privacy agreement, check storage permission
                    activity.checkStoragePermissionAndExecute {
                        val intent = AntivirusScanActivity.createIntent(context, scanType)
                        context.startActivity(intent)
                    }
                }
                pendingScanType = null
            }
        )
    }
}