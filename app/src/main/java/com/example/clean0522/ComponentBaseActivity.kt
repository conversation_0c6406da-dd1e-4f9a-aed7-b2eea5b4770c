package com.example.clean0522

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.example.clean0522.ui.components.NotificationPermissionDialog
import com.example.clean0522.ui.components.StoragePermissionDialog
import com.example.clean0522.utils.PermissionUtils

abstract class ComponentBaseActivity: ComponentActivity() {

    private var showStoragePermissionDialog by mutableStateOf(false)
    private var storagePendingAction: (() -> Unit)? = null

    private var showNotificationPermissionDialog by mutableStateOf(false)
    private var notificationPendingAction: (() -> Unit)? = null

    private val storagePermissionSettingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { _ ->
        if (PermissionUtils.hasStoragePermission(this)) {
            storagePendingAction?.invoke()
        }
        storagePendingAction = null
    }

    private val notificationPermissionSettingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { _ ->
        if (PermissionUtils.hasNotificationPermission(this)) {
            notificationPendingAction?.invoke()
        }
        notificationPendingAction = null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            setHomePage()

            if (showStoragePermissionDialog) {
                StoragePermissionDialog(
                    onDismiss = {
                        showStoragePermissionDialog = false
                        storagePendingAction = null
                    },
                    onAllow = {
                        showStoragePermissionDialog = false
                        val intent = PermissionUtils.getStoragePermissionIntent(this@ComponentBaseActivity)
                        storagePermissionSettingsLauncher.launch(intent)
                    }
                )
            }

            if (showNotificationPermissionDialog) {
                NotificationPermissionDialog(
                    onDismiss = {
                        showNotificationPermissionDialog = false
                        notificationPendingAction = null
                    },
                    onAllow = {
                        showNotificationPermissionDialog = false
                        val intent = PermissionUtils.getNotificationPermissionIntent(this@ComponentBaseActivity)
                        notificationPermissionSettingsLauncher.launch(intent)
                    }
                )
            }
        }
    }

    @Composable
    abstract fun setHomePage()

    /**
     * Check storage permission and execute action if granted, or show permission dialog
     * @param action The action to execute if permission is granted
     */
    fun checkStoragePermissionAndExecute(action: () -> Unit) {
        if (PermissionUtils.hasStoragePermission(this)) {
            action()
        } else {
            storagePendingAction = action
            showStoragePermissionDialog = true
        }
    }

    /**
     * Check notification permission and execute action if granted, or show permission dialog
     * @param action The action to execute if permission is granted
     */
    fun checkNotificationPermissionAndExecute(action: () -> Unit) {
        if (PermissionUtils.hasNotificationPermission(this)) {
            action()
        } else {
            notificationPendingAction = action
            showNotificationPermissionDialog = true
        }
    }
}