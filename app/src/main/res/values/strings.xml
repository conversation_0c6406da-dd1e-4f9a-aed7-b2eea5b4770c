<resources>
    <!-- App -->
    <string name="app_name">Clean0522</string>

    <!-- Main Navigation -->
    <string name="nav_files">Files</string>
    <string name="nav_antivirus">Antivirus</string>
    <string name="nav_more">More</string>
    <string name="nav_settings">Settings</string>

    <!-- More Screen -->
    <string name="tools">Tools</string>

    <!-- Tool Items -->
    <string name="tool_device_info">Device Info</string>
    <string name="tool_ram_usage">RAM Usage</string>
    <string name="tool_battery_info">Battery Info</string>
    <string name="tool_cpu_monitor">CPU Monitor</string>
    <string name="tool_app_manager">App Manager</string>
    <string name="tool_app_process">App Process</string>
    <string name="tool_network">Network</string>
    <string name="tool_similar_photos">Similar Photos</string>

    <!-- Common -->
    <string name="back">Back</string>
    <string name="settings">Settings</string>
    <string name="browse">Browse</string>
    <string name="coming_soon">Coming Soon</string>
    <string name="unknown_error">Unknown error occurred</string>
    <string name="error">Error</string>
    <string name="retry">Retry</string>
    <string name="ok">OK</string>
    <string name="delete">Delete</string>
    <string name="rename">Rename</string>
    <string name="share">Share</string>
    <string name="details">Details</string>
    <string name="select_all">Select All</string>

    <!-- System Info -->
    <string name="system_info_title">System Information</string>
    <string name="device_information">Device Information</string>
    <string name="ram_usage_monitor">RAM Usage Monitor</string>
    <string name="battery_information">Battery Information</string>
    <string name="cpu_monitor_title">CPU Monitor</string>
    <string name="network_monitor">Network Monitor</string>
    <string name="app_manager_title">App Manager</string>
    <string name="app_process_monitor">App Process Monitor</string>
    <string name="manage_installed_applications">Manage installed applications</string>
    <string name="monitor_running_processes">Monitor running processes</string>

    <!-- RAM Usage -->
    <string name="ram_used">used</string>
    <string name="ram_total">total</string>
    <string name="ram_usage_chart">RAM Usage Chart</string>
    <string name="ram_usage_percentage">%1$d%%</string>
    <string name="here_are_some_other_features">Here are some other features you might like.</string>
    <string name="device_storage">Device Storage</string>
    <string name="security_scan">Security Scan</string>
    <string name="battery_info">Battery Info</string>
    <string name="ram_usage">RAM Usage</string>

    <!-- Battery Info -->
    <string name="battery_health_good">Good</string>
    <string name="battery_health_overheat">Overheat</string>
    <string name="battery_health_dead">Dead</string>
    <string name="battery_health_over_voltage">Over Voltage</string>
    <string name="battery_health_unspecified_failure">Unspecified Failure</string>
    <string name="battery_health_cold">Cold</string>
    <string name="battery_health_unknown">Unknown</string>
    <string name="battery_status_charging">Charging</string>
    <string name="battery_status_discharging">Discharging</string>
    <string name="battery_status_not_charging">Not Charging</string>
    <string name="battery_status_full">Full</string>
    <string name="battery_status_unknown">Unknown</string>
    <string name="battery_current">Current</string>
    <string name="battery_capacity">Capacity</string>
    <string name="battery_source">Source</string>
    <string name="battery_voltage">Voltage</string>
    <string name="battery_temperature">Temperature</string>
    <string name="battery_technology">Technology</string>
    <string name="battery_percentage">%1$d%%</string>
    <string name="battery_current_ma">%1$d mA</string>
    <string name="battery_capacity_mah">%1$d mAh</string>
    <string name="battery_voltage_mv">%1$d mV</string>
    <string name="battery_temperature_celsius">%1$.1f °C</string>
    <string name="battery_source_battery">Battery</string>
    <string name="battery_source_ac">AC</string>
    <string name="battery_source_usb">USB</string>
    <string name="battery_source_wireless">Wireless</string>
    <string name="battery_image_placeholder">Battery Image</string>

    <!-- CPU Monitor -->
    <string name="cpu_cores_count">%1$d cores</string>
    <string name="cpu_core_name">core%1$d</string>
    <string name="cpu_frequency_mhz">%1$d MHz</string>
    <string name="cpu_frequency_ghz">%.2f GHz</string>
    <string name="cpu_governor">CPU governor</string>
    <string name="cpu_hardware">CPU hardware</string>
    <string name="cpu_fabrication">Fabrication</string>
    <string name="cpu_frequency">Frequency</string>
    <string name="gpu_renderer">GPU renderer</string>
    <string name="gpu_vendor">GPU vendor</string>
    <string name="gpu_version">GPU version</string>
    <string name="cpu_processor">Processor</string>
    <string name="cpu_strut">Strut</string>
    <string name="cpu_supported_abis">Supported ABIs</string>
    <string name="cpu_vulkan">Vulkan</string>
    <string name="cpu_unknown">Unknown</string>

    <!-- Device Info Items -->
    <string name="device_model">Device Model</string>
    <string name="android_version">Android Version</string>
    <string name="api_level">API Level</string>
    <string name="manufacturer">Manufacturer</string>
    <string name="total_storage">Total Storage</string>
    <string name="available_storage">Available Storage</string>
    <string name="ram">RAM</string>
    <string name="processor">Processor</string>
    <string name="android_device_id">Android device ID</string>
    <string name="bluetooth_mac_address">Bluetooth MAC address</string>
    <string name="board">Board</string>
    <string name="brand">Brand</string>
    <string name="build_fingerprint">Build fingerprint</string>
    <string name="device">Device</string>
    <string name="device_name">Device name</string>
    <string name="hardware">Hardware</string>
    <string name="hardware_serial">Hardware serial</string>
    <string name="model">Model</string>
    <string name="usb_debugging">USB debugging</string>
    <string name="wifi_mac_address">WiFi MAC address</string>
    <string name="device_image_placeholder">Device Image</string>

    <!-- Image Loading -->
    <string name="loading_image">Loading image</string>
    <string name="error_loading_image">Error loading image</string>

    <!-- Similar Photos -->
    <string name="similar_group_title">Similar Group %1$d</string>
    <string name="enhanced_similar_photo_detection">Enhanced Similar Photo Detection</string>
    <string name="mediastore_image_scan">MediaStore Image Scan</string>
    <string name="hash_calculation">Hash Calculation</string>
    <string name="similarity_grouping">Similarity Grouping</string>
    <string name="found_quality_images">Found %1$d quality images for analysis</string>
    <string name="not_enough_images">Not enough images for similarity detection</string>
    <string name="calculated_hashes">Calculated hashes for %1$d images</string>
    <string name="found_similar_groups">Found %1$d groups of similar images</string>
    <string name="found_similar_images">Found similar images: %1$s &lt;-&gt; %2$s</string>
    <string name="starting_detection">Starting enhanced similar photo detection…</string>
    <string name="error_in_detection">Error in enhanced similar photo detection</string>

    <!-- File Browser Types -->
    <string name="browser_storage">Storage Browser</string>
    <string name="browser_images">Images</string>
    <string name="browser_videos">Videos</string>
    <string name="browser_audios">Audios</string>
    <string name="browser_docs">Docs</string>
    <string name="browser_zips">Zips</string>
    <string name="browser_archives">Archives</string>
    <string name="browser_apks">APKs</string>
    <string name="browser_file">File Browser</string>

    <!-- Info String -->
    <string name="used">used</string>

    <!-- File Browser Content -->
    <string name="content_storage_browser">Storage Browser - Coming Soon</string>
    <string name="content_images_browser">Images Browser - Coming Soon</string>
    <string name="content_videos_browser">Videos Browser - Coming Soon</string>
    <string name="content_audios_browser">Audios Browser - Coming Soon</string>
    <string name="content_docs_browser">Documents Browser - Coming Soon</string>
    <string name="content_archives_browser">Archives Browser - Coming Soon</string>
    <string name="content_apks_browser">APKs Browser - Coming Soon</string>

    <!-- File Utility Content -->
    <string name="content_large_files">Large Files - Coming Soon</string>
    <string name="content_recent_files">Recent Files - Coming Soon</string>
    <string name="content_duplicate_files">Duplicate Files - Coming Soon</string>
    <string name="content_redundant_files">Redundant Files - Coming Soon</string>
    <string name="content_similar_photos">Similar Photos - Coming Soon</string>

    <!-- File Management -->
    <string name="file_management">File Management</string>

    <!-- File Browser -->
    <string name="file_item_count_size">%1$d items %2$s</string>
    <string name="file_operation_detail">detail</string>
    <string name="file_operation_open">open</string>
    <string name="file_operation_share">share</string>
    <string name="file_operation_rename">rename</string>
    <string name="file_operation_remove">remove</string>
    <string name="remove">Remove</string>
    <string name="remove_with_size">Remove %1$s</string>
    <string name="confirm_delete">Confirm Delete</string>
    <string name="confirm_delete_message">Are you sure you want to delete the selected files?</string>
    <string name="rename_file">Rename File</string>
    <string name="new_file_name">New file name</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Confirm</string>
    <string name="file_details">File Details</string>
    <string name="file_name">Name: %1$s</string>
    <string name="file_path">Path: %1$s</string>
    <string name="file_size">Size: %1$s</string>
    <string name="file_modified">Modified: %1$s</string>
    <string name="file_type">Type: %1$s</string>
    <string name="storage">storage</string>

    <!-- File Utility -->
    <string name="utility_file">File Utility</string>
    <string name="utility_large_files">Large Files</string>
    <string name="utility_recent_files">Recent Files</string>
    <string name="utility_duplicate_files">Duplicate Files</string>
    <string name="utility_redundant_files">Redundant Files</string>
    <string name="utility_similar_photos">Similar Photos</string>

    <!-- File Groups -->
    <string name="group_apks">APKs</string>
    <string name="group_download_files">Download Files</string>
    <string name="group_screenshots">Screenshots</string>
    <string name="group_log_files">Log Files</string>
    <string name="group_temp_files">Temp Files</string>

    <!-- File Types -->
    <string name="type_all">All</string>
    <string name="type_images">images</string>
    <string name="type_audios">audios</string>
    <string name="type_videos">videos</string>
    <string name="type_others">others</string>

    <!-- Messages -->
    <string name="no_large_files_found">No large files found</string>
    <string name="no_recent_files_found">No recent files found</string>
    <string name="no_duplicate_files_found">No duplicate files found</string>
    <string name="no_redundant_files_found">No redundant files found</string>
    <string name="no_similar_photos_found">No similar photos found</string>
    <string name="expand">Expand</string>
    <string name="collapse">Collapse</string>
    <string name="sort">Sort</string>

    <!-- Document Types -->
    <string name="document_type_all">All</string>
    <string name="document_type_pdf">PDF</string>
    <string name="document_type_word">Word</string>
    <string name="document_type_ppt">PPT</string>
    <string name="document_type_excel">Excel</string>
    <string name="document_type_txt">TXT</string>
    <string name="document_type_other">Other</string>

    <!-- Media Browser -->
    <string name="loading">Loading...</string>
    <string name="empty_folder">No files found</string>

    <!-- Sort Options -->
    <string name="sort_by">Sort by</string>
    <string name="sort_name_a_to_z">Name A to Z</string>
    <string name="sort_name_z_to_a">Name Z to A</string>
    <string name="sort_newest_first">Newest First</string>
    <string name="sort_oldest_first">Oldest First</string>
    <string name="sort_small_to_large">Small to Large</string>
    <string name="sort_large_to_small">Large to Small</string>
    <string name="close">Close</string>

    <!-- App Process -->
    <string name="running_apps_count">%1$d running apps</string>
    <string name="non_stopped_apps">Non-stopped apps</string>
    <string name="stop">Stop</string>
    <string name="stopped">Stopped</string>
    <string name="no_running_apps">No running apps</string>
    <string name="no_running_apps_description">All user apps are currently stopped or no apps are running.</string>

    <!-- App Manager -->
    <string name="tab_all">All</string>
    <string name="tab_system_apps">system apps</string>
    <string name="tab_installed_apps">installed apps</string>
    <string name="info_app_name">app name</string>
    <string name="more">More</string>
    <string name="no_apps_found">No apps found</string>
    <string name="loading_apps">Loading apps...</string>
    <string name="app_version">Version %1$s</string>
    <string name="app_size_mb">%1$s MB</string>
    <string name="app_install_date">%1$s</string>
    <string name="system_app">System App</string>
    <string name="user_app">User App</string>
    <string name="apps_count">%1$d apps</string>

    <!-- App Details -->
    <string name="app_details_title">App Details</string>
    <string name="tab_general">General</string>
    <string name="tab_permission">Permission</string>
    <string name="tab_certificates">Certificates</string>
    <string name="system_settings">System Settings</string>
    <string name="google_play">Google Play</string>

    <!-- App General Info -->
    <string name="apk_size">APK Size</string>
    <string name="application_name">Application name</string>
    <string name="min_sdk">Min SDK</string>
    <string name="min_sdk_version">Min SDK Version</string>
    <string name="package_name">Package name</string>
    <string name="process_name">Process name</string>
    <string name="system_application">System application</string>
    <string name="target_sdk">Target SDK</string>
    <string name="target_sdk_version">Target SDK Version</string>
    <string name="version_code">Version code</string>
    <string name="version_name">Version name</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>

    <!-- App Permissions -->
    <string name="no_permissions_found">No permissions found</string>
    <string name="permission_granted">Granted</string>
    <string name="permission_denied">Not Granted</string>
    <string name="protection_level">Protection level:</string>
    <string name="constant_value">Constant Value:</string>
    <string name="grant_status">Grant status:</string>

    <!-- App Certificates -->
    <string name="sign_algorithm">Sign algorithm</string>
    <string name="valid_from">Valid from</string>
    <string name="valid_to">Valid to</string>
    <string name="public_key_md5">Public key MD5</string>
    <string name="certificate_md5">Certificate MD5</string>
    <string name="serial_number">Serial number</string>
    <string name="issuer_name">Issuer name</string>
    <string name="issuer_organization">Issuer organization</string>
    <string name="issuer_country">Issuer country</string>
    <string name="subject_name">Subject name</string>
    <string name="subject_organization">Subject organization</string>
    <string name="subject_country">Subject country</string>
    <string name="no_certificates_found">No certificates found</string>

    <!-- Network Info -->
    <string name="wifi_section">WiFi</string>
    <string name="mobile_data_section">Mobile Data</string>
    <string name="wifi_not_connected">WiFi not connected</string>
    <string name="mobile_data_not_available">Mobile data not available</string>

    <!-- WiFi Info -->
    <string name="wifi_ssid">SSID</string>
    <string name="wifi_dhcp">DHCP</string>
    <string name="wifi_dhcp_lease_duration">DHCP lease duration</string>
    <string name="wifi_dns_1">DNS 1</string>
    <string name="wifi_dns_2">DNS 2</string>
    <string name="wifi_frequency">Frequency</string>
    <string name="wifi_gateway">Gateway</string>
    <string name="wifi_interface">Interface</string>
    <string name="wifi_ip">IP</string>
    <string name="wifi_ipv6">IPv6</string>
    <string name="wifi_link_speed">Link speed</string>
    <string name="wifi_netmask">Netmask</string>
    <string name="wifi_quality">Quality</string>
    <string name="wifi_safety">Safety</string>
    <string name="wifi_strength">Strength</string>
    <string name="wifi_direct">WiFi Direct</string>

    <!-- Mobile Data Info -->
    <string name="mobile_multi_sim">Multi SIM</string>
    <string name="mobile_status">Status</string>
    <string name="mobile_network_type">Network Type</string>
    <string name="mobile_operator">Operator</string>
    <string name="mobile_country_code">Country Code</string>
    <string name="mobile_network_code">Network Code</string>

    <!-- Network Status Values -->
    <string name="network_connected">Connected</string>
    <string name="network_disconnected">Disconnected</string>
    <string name="network_enabled">Enabled</string>
    <string name="network_disabled">Disabled</string>
    <string name="network_supported">Supported</string>
    <string name="network_not_supported">Not Supported</string>
    <string name="network_unknown">Unknown</string>
    <string name="network_unavailable">Unavailable</string>

    <!-- WiFi Security Types -->
    <string name="wifi_security_open">Open</string>
    <string name="wifi_security_wep">WEP</string>
    <string name="wifi_security_wpa">WPA</string>
    <string name="wifi_security_wpa2">WPA2</string>
    <string name="wifi_security_wpa3">WPA3</string>
    <string name="wifi_security_unknown">Unknown</string>

    <!-- Units -->
    <string name="unit_mhz">%1$d MHz</string>
    <string name="unit_mbps">%1$d Mbps</string>
    <string name="unit_dbm">%1$d dBm</string>
    <string name="unit_percent">%1$d%%</string>
    <string name="unit_seconds">%1$d seconds</string>

    <!-- Antivirus -->
    <string name="antivirus_title">Antivirus</string>
    <string name="antivirus_subtitle">Keep your device safe with regular Antivirus Scans.</string>
    <string name="antivirus_quick_scan">Quick Scan</string>
    <string name="antivirus_quick_scan_desc">Run a quick scan for common threats</string>
    <string name="antivirus_folder_scan">Folder Scan</string>
    <string name="antivirus_folder_scan_desc">Scan specific folders for viruses</string>
    <string name="antivirus_complete_scan">Complete Scan</string>
    <string name="antivirus_complete_scan_desc">Complete scan for every corner</string>
    <string name="antivirus_scan_now">Scan Now</string>
    <string name="antivirus_about_title">About Antivirus</string>
    <string name="antivirus_about_content">The antivirus feature uses the Trustlook Antivirus SDK to scan for threats when you initiate a manual scan.\n\nWhat data is processed during scanning:\n● App name and package name\n● Version code and version name\n● APK and signature MD5 hash\n● File path and installation details\n\nHow your data is handled:\n● Data is encrypted during transmission\n● No personal data like usernames, passwords, or messages is collected\n● Data is used only for threat detection and retained temporarily\n● No data is sold or shared with unauthorized third parties\n● Trustlook SDK complies with privacy regulations and our Terms of Service\n\nFor more, see the %1$s</string>
    <string name="trustlook_privacy_policy_link">[Trustlook Privacy Policy]</string>

    <!-- Antivirus Scan -->
    <string name="scan_in_progress">Antivirus Scan in Progress…</string>
    <string name="scanning_files">Scanning files for threats…</string>
    <string name="scan_completed">Scan Completed</string>
    <string name="scan_cancelled">Scan Cancelled</string>
    <string name="scan_error">Scan Error</string>
    <string name="items_scanned">%1$d items scanned</string>
    <string name="threats_found">%1$d threats found</string>
    <string name="no_threats_found">No threats found</string>
    <string name="device_is_secure">Your device is secure!</string>
    <string name="threats_detected">Threats detected!</string>
    <string name="delete_or_ignore_threats">Delete or ignore items to keep your device secure.</string>

    <!-- Threat Levels -->
    <string name="threat_level_malware">Malware</string>
    <string name="threat_level_pua">PUA</string>
    <string name="threat_level_safe">Safe</string>

    <!-- Threat Actions -->
    <string name="ignore">Ignore</string>
    <string name="ignored">Ignored</string>
    <string name="delete_threat">Delete</string>
    <string name="uninstall">Uninstall</string>

    <!-- Scan Progress -->
    <string name="initializing_scan">Initializing scan…</string>
    <string name="scan_progress">%1$d%% completed</string>
    <string name="cancel_scan">Cancel Scan</string>

    <!-- Scan Results -->
    <string name="scan_summary">Scan Summary</string>
    <string name="total_files_scanned">Total files scanned: %1$d</string>
    <string name="threats_found_count">Threats found: %1$d</string>
    <string name="scan_duration">Scan duration: %1$s</string>
    <string name="scan_type_quick">Quick Scan</string>
    <string name="scan_type_folder">Folder Scan</string>
    <string name="scan_type_complete">Complete Scan</string>


    <!-- Ignore List -->
    <string name="ignore_list">Ignore list</string>
    <string name="ignore_list_description">Files or apps you exclude from scans will show up here.</string>
    <string name="unignore">Unignore</string>
    <string name="no_ignored_threats">No ignored threats</string>
    <string name="ignored_threats_will_appear_here">Threats you ignore will appear here.</string>

    <!-- Delete Confirmation -->
    <string name="delete_file_title">Delete File</string>
    <string name="delete_file_message">Are you sure you want to delete this file? This action cannot be undone.</string>

    <!-- Storage Permission -->
    <string name="storage_permission_title">All Files Permission</string>
    <string name="storage_permission_message">To enable file management and scan your device for threats, we need permission to access all files.</string>
    <string name="allow">Allow</string>

    <!-- Notification Permission -->
    <string name="notification_permission_title">Notification Permission</string>
    <string name="notification_permission_message">To keep you informed about scan results and app updates, we need permission to send notifications.</string>

    <!-- Settings -->
    <string name="notification">Notification</string>
    <string name="developer_email">Developer Email</string>
    <string name="terms_of_use">Terms of Use</string>
    <string name="privacy_policy">Privacy Policy</string>

    <!-- Splash Screen -->
    <string name="accept">Accept</string>
    <string name="splash_terms_message">By continuing you\'re accepting our %1$s and %2$s</string>
    <string name="terms_of_use_link">Terms of Use</string>
    <string name="privacy_policy_link">Privacy Policy</string>

    <!-- URLs -->
    <string name="terms_of_use_url">https://example.com/terms</string>
    <string name="privacy_policy_url">https://example.com/privacy</string>
</resources>