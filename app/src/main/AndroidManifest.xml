<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
                     android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
                     tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:ignore="QueryAllPackagesPermission"/>

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- Media permissions for Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- Notification permission for Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
            android:name=".CleanApp"
            android:allowBackup="true"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:supportsRtl="true"
            android:theme="@style/Theme.Clean0522"
            tools:targetApi="31">
        <activity
                android:name=".SplashActivity"
                android:exported="true"
                android:label="@string/app_name"
                android:theme="@style/Theme.Clean0522">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <activity
                android:name=".MainActivity"
                android:exported="false"
                android:label="@string/app_name"
                android:theme="@style/Theme.Clean0522" />

        <meta-data
                android:value="c2100aa821d7aa3721fa750b13694b74aae4a67f531a8933c41b7ea4"
                android:name="com.trustlook.ApiKey" />

        <activity
                android:name=".FileBrowserActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <activity
                android:name=".FileUtilityActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <activity
                android:name=".SystemInfoActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <activity
                android:name=".AppInfoActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <activity
                android:name=".AppDetailActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <activity
                android:name=".AntivirusScanActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <activity
                android:name=".ui.features.antivirus.IgnoreListActivity"
                android:exported="false"
                android:theme="@style/Theme.Clean0522" />

        <!-- FileProvider for sharing files -->
        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.provider"
                android:exported="false"
                android:grantUriPermissions="true">
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>