# UI Component Reuse Strategy

## Overview
This document outlines the strategy for creating reusable UI components across the Android cleaning application. By implementing a robust component reuse strategy, we can maintain consistency, reduce development time, and ensure a cohesive user experience.

## Core Reusable Components

### 1. File Browser Components

#### Media Browser Template
A reusable template for browsing media files (images and videos):

```kotlin
@Composable
fun MediaBrowserTemplate(
    mediaItems: List<MediaItem>,
    onItemClick: (MediaItem) -> Unit,
    onItemLongClick: (MediaItem) -> Unit,
    selectionMode: Boolean = false,
    selectedItems: Set<String> = emptySet(),
    onSelectionChange: (String, Boolean) -> Unit = { _, _ -> },
    emptyContent: @Composable () -> Unit = { DefaultEmptyContent() },
    loadingContent: @Composable () -> Unit = { DefaultLoadingContent() },
    gridColumns: Int = 3,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

This template will be used for:
- Images Browser
- Videos Browser

#### Document Browser Template
A reusable template for browsing document files:

```kotlin
@Composable
fun DocumentBrowserTemplate(
    documentItems: List<DocumentItem>,
    onItemClick: (DocumentItem) -> Unit,
    onItemLongClick: (DocumentItem) -> Unit,
    selectionMode: Boolean = false,
    selectedItems: Set<String> = emptySet(),
    onSelectionChange: (String, Boolean) -> Unit = { _, _ -> },
    emptyContent: @Composable () -> Unit = { DefaultEmptyContent() },
    loadingContent: @Composable () -> Unit = { DefaultLoadingContent() },
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

This template will be used for:
- Audios Browser
- Documents Browser
- Archives Browser
- APKs Browser

#### Storage Browser Component
A specialized component for browsing the file system:

```kotlin
@Composable
fun StorageBrowserComponent(
    currentPath: String,
    items: List<FileSystemItem>,
    onNavigateUp: () -> Unit,
    onItemClick: (FileSystemItem) -> Unit,
    onItemLongClick: (FileSystemItem) -> Unit,
    // Additional parameters
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

### 2. File Item Components

#### Media Item Component
```kotlin
@Composable
fun MediaItemComponent(
    item: MediaItem,
    selected: Boolean = false,
    onSelect: (Boolean) -> Unit = {},
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    selectionMode: Boolean = false,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

#### Document Item Component
```kotlin
@Composable
fun DocumentItemComponent(
    item: DocumentItem,
    selected: Boolean = false,
    onSelect: (Boolean) -> Unit = {},
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    selectionMode: Boolean = false,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

#### File System Item Component
```kotlin
@Composable
fun FileSystemItemComponent(
    item: FileSystemItem,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

### 3. Scanning Components

#### Scan Progress Component
A reusable component for displaying scan progress:

```kotlin
@Composable
fun ScanProgressComponent(
    progress: Float,
    currentItem: String? = null,
    itemCount: Int = 0,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

This component will be used for:
- Virus scanning
- File scanning
- Duplicate detection
- Similar photo detection

#### Scan Results Component
```kotlin
@Composable
fun <T> ScanResultsComponent(
    items: List<T>,
    itemContent: @Composable (T) -> Unit,
    emptyContent: @Composable () -> Unit = { DefaultEmptyContent() },
    headerContent: @Composable () -> Unit = {},
    footerContent: @Composable () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

### 4. Information Display Components

#### Status Card Component
```kotlin
@Composable
fun StatusCardComponent(
    title: String,
    value: String,
    icon: @Composable () -> Unit,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

#### Progress Card Component
```kotlin
@Composable
fun ProgressCardComponent(
    title: String,
    progress: Float,
    startValue: String,
    endValue: String,
    icon: @Composable () -> Unit,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    progressColor: Color = MaterialTheme.colorScheme.primary,
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

#### Information List Component
```kotlin
@Composable
fun <T> InformationListComponent(
    items: List<T>,
    itemContent: @Composable (T) -> Unit,
    headerContent: @Composable () -> Unit = {},
    emptyContent: @Composable () -> Unit = { DefaultEmptyContent() },
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

### 5. Action Components

#### Action Button Component
```kotlin
@Composable
fun ActionButtonComponent(
    text: String,
    onClick: () -> Unit,
    icon: @Composable () -> Unit,
    enabled: Boolean = true,
    loading: Boolean = false,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

#### Action Card Component
```kotlin
@Composable
fun ActionCardComponent(
    title: String,
    description: String,
    icon: @Composable () -> Unit,
    onClick: () -> Unit,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

#### Feature Grid Component
```kotlin
@Composable
fun FeatureGridComponent(
    features: List<Feature>,
    columns: Int = 2,
    onClick: (Feature) -> Unit,
    modifier: Modifier = Modifier
) {
    // Implementation
}
```

## Component Reuse Across Activities

### 1. File Browser Activity
- Uses **MediaBrowserTemplate** for Images and Videos
- Uses **DocumentBrowserTemplate** for Audios, Docs, Zips, and APKs
- Uses **StorageBrowserComponent** for Storage Browser

### 2. File Utility Activity
- Uses **ScanProgressComponent** for scanning operations
- Uses **ScanResultsComponent** for displaying results
- Uses **ActionButtonComponent** for actions
- Uses **MediaItemComponent** for Similar Photos
- Uses **DocumentItemComponent** for file listings

### 3. System Info Activity
- Uses **StatusCardComponent** for displaying metrics
- Uses **ProgressCardComponent** for usage visualization
- Uses **InformationListComponent** for detailed information
- Uses **ActionButtonComponent** for optimization actions

### 4. App Info Activity
- Uses **InformationListComponent** for app listings
- Uses **StatusCardComponent** for app metrics
- Uses **ActionButtonComponent** for app actions

### 5. Virus Scan Activity
- Uses **ScanProgressComponent** for scan progress
- Uses **ScanResultsComponent** for threat listings
- Uses **ActionButtonComponent** for threat resolution

### 6. Main Activity
- Uses **FeatureGridComponent** for feature listings
- Uses **StatusCardComponent** for status display
- Uses **ActionCardComponent** for quick actions

### 7. Operation Complete Activity
- Uses **StatusCardComponent** for operation summary
- Uses **ActionButtonComponent** for follow-up actions

## Implementation Guidelines

### 1. Component Creation
- Create all reusable components in a dedicated `ui/common` package
- Implement preview functions for all components
- Document component parameters and usage

### 2. Component Usage
- Import components from the common package
- Customize through parameters rather than creating new components
- Use composition for complex UI structures

### 3. Theme Integration
- Ensure all components respect the application theme
- Use MaterialTheme colors and typography
- Support both light and dark themes

### 4. Accessibility
- Implement content descriptions for all components
- Support different text sizes
- Ensure sufficient contrast ratios

### 5. Testing
- Create UI tests for all reusable components
- Test components in isolation
- Test components in the context of their parent screens
