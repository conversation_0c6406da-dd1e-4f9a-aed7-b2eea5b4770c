# Android Cleaning Application Project Structure

## Core Features and Screens

### 1. Splash Screen
- App initialization
- Branding display
- Initial loading

### 2. Main Screen (with Bottom Navigation)
#### Files Tab
- Storage usage visualization
- Quick access to file tools
- Recent cleaning history

#### Antivirus Tab
- Security status overview
- Quick scan button
- Threat detection statistics

#### More Tab
- Additional features access
- System tools
- App management tools

#### Settings Screen
- App preferences
- Notification settings
- Theme configuration

### 3. File Browser Screens
- Media browsers (Images, Videos)
- Document browsers (Audios, Docs, Zips, APKs)
- Storage browser (File system navigation)

### 4. File Utility Screens
- Large files identification
- Recent files management
- Duplicate files detection
- Redundant files cleaning
- Similar photos management

### 5. System Information Screens
- RAM usage monitoring
- Battery information
- CPU monitoring
- App details
- Network monitoring

### 6. App Management Screens
- App manager
- App process monitoring

### 7. Virus Scanning Screens
- Scanning animation
- Ignore list management
- Scan completion results

### 8. Operation Complete Screen
- Operation summary
- Statistics display
- Further recommendations

## Component Structure

### Domain Layer

#### Models
- `StorageInfo`: Information about device storage
- `JunkFile`: Represents a junk file with metadata
- `AppInfo`: Information about installed applications
- `CleaningTask`: Represents a cleaning operation
- `MemoryInfo`: Device memory information
- `BatteryInfo`: Battery usage information

#### Use Cases
- `ScanJunkFilesUseCase`: Scans for junk files
- `CleanJunkFilesUseCase`: Cleans identified junk files
- `AnalyzeStorageUseCase`: Analyzes storage usage
- `OptimizeMemoryUseCase`: Optimizes device memory
- `FindDuplicateFilesUseCase`: Finds duplicate files
- `AnalyzeBatteryUsageUseCase`: Analyzes battery usage

#### Repositories
- `FileRepository`: Interface for file operations
- `AppRepository`: Interface for app-related operations
- `SystemRepository`: Interface for system-level operations
- `SettingsRepository`: Interface for user settings

### Data Layer

#### Local Data Sources
- `FileSystemDataSource`: Accesses file system
- `AppDatabaseDataSource`: Accesses app information
- `SystemInfoDataSource`: Retrieves system information
- `UserPreferencesDataSource`: Manages user preferences

#### Repository Implementations
- `FileRepositoryImpl`: Implements `FileRepository`
- `AppRepositoryImpl`: Implements `AppRepository`
- `SystemRepositoryImpl`: Implements `SystemRepository`
- `SettingsRepositoryImpl`: Implements `SettingsRepository`

### Presentation Layer

#### ViewModels
- `DashboardViewModel`: Manages dashboard state
- `FileCleaningViewModel`: Manages file cleaning operations
- `MemoryOptimizationViewModel`: Manages memory optimization
- `PrivacyProtectionViewModel`: Manages privacy features
- `BatteryOptimizationViewModel`: Manages battery optimization
- `SettingsViewModel`: Manages app settings

#### UI States
- `DashboardState`: State for dashboard UI
- `FileCleaningState`: State for file cleaning UI
- `MemoryOptimizationState`: State for memory optimization UI
- `PrivacyProtectionState`: State for privacy protection UI
- `BatteryOptimizationState`: State for battery optimization UI
- `SettingsState`: State for settings UI

#### UI Components
- `StorageUsageChart`: Visualizes storage usage
- `JunkFileList`: Displays list of junk files
- `AppList`: Displays list of applications
- `CleaningProgressIndicator`: Shows cleaning progress
- `MemoryUsageGraph`: Visualizes memory usage
- `BatteryUsageChart`: Visualizes battery usage

## Common Components

### UI Components
- `ActionButton`: Reusable button for actions
- `StatusCard`: Card displaying status information
- `ProgressIndicator`: Custom progress indicator
- `InfoDialog`: Dialog for showing information
- `ConfirmationDialog`: Dialog for confirmations
- `EmptyStateView`: View for empty states
- `ErrorView`: View for error states

### Utilities
- `FileUtils`: Utilities for file operations
- `PermissionUtils`: Utilities for permission handling
- `DateTimeUtils`: Utilities for date and time operations
- `SizeUtils`: Utilities for size calculations and formatting
- `NotificationUtils`: Utilities for notifications

## Navigation
- `AppNavHost`: Main navigation component
- `Screen`: Sealed class defining app screens
- `NavigationActions`: Actions for navigation

## Dependency Injection
- `AppModule`: Main application module
- `RepositoryModule`: Provides repository implementations
- `UseCaseModule`: Provides use cases
- `ViewModelModule`: Provides ViewModels
- `DataSourceModule`: Provides data sources

## Testing Structure
- Unit tests for each use case
- Unit tests for repository implementations
- UI tests for each feature
- Integration tests for key workflows
