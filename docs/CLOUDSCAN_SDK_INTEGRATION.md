# CloudScan SDK Integration Guide

## Overview
This document provides instructions for integrating the Trustlook CloudScan SDK into the Clean0522 Android application.

## SDK Setup

### 1. Add SDK Files
Place the CloudScan SDK files in the `app/libs` directory:
- `cloudscan-sdk.aar` (or `.jar`)
- Any additional dependency files

### 2. Update build.gradle.kts
The project is already configured to include files from the libs directory:
```kotlin
implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
```

### 3. Add Required Permissions
Add these permissions to `AndroidManifest.xml` if not already present:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

## Code Integration

### 1. Import SDK Classes
Add these imports to `CloudScanManager.kt`:
```kotlin
import com.trustlook.sdk.cloudscan.CloudScanClient
import com.trustlook.sdk.cloudscan.CloudScanListener
import com.trustlook.sdk.cloudscan.AppInfo
import com.trustlook.sdk.cloudscan.Region
```

### 2. Uncomment SDK Code
In `CloudScanManager.kt`, uncomment the following sections:

#### Initialize CloudScan Client:
```kotlin
cloudScanClient = CloudScanClient.Builder(context)
    .setRegion(Region.INTL) // or Region.BAIDU for China
    .setConnectionTimeout(CONNECTION_TIMEOUT)
    .setSocketTimeout(SOCKET_TIMEOUT)
    .build()
```

#### Quick Scan Implementation:
```kotlin
cloudScanClient?.startQuickScan(object : CloudScanListener {
    override fun onScanStarted() {
        trySend(ScanProgress.Started)
    }
    
    override fun onScanProgress(curr: Int, total: Int, result: AppInfo?) {
        val progress = if (total > 0) (curr * 100) / total else 0
        trySend(ScanProgress.Progress(curr, total, progress, result?.let { convertAppInfo(it) }))
    }
    
    override fun onScanError(errCode: Int, errMessage: String?) {
        trySend(ScanProgress.Error(errCode, errMessage ?: "Unknown error"))
    }
    
    override fun onScanCanceled() {
        trySend(ScanProgress.Cancelled)
    }
    
    override fun onScanInterrupt() {
        trySend(ScanProgress.Interrupted)
    }
    
    override fun onScanFinished(results: List<AppInfo>?) {
        val threats = results?.mapNotNull { convertAppInfo(it) } ?: emptyList()
        trySend(ScanProgress.Completed(threats))
    }
})
```

#### Folder Scan Implementation:
```kotlin
cloudScanClient?.startFolderScan(folderPath, object : CloudScanListener {
    // Same CloudScanListener implementation as above
})
```

#### Comprehensive Scan Implementation:
```kotlin
cloudScanClient?.startComprehensiveScan(object : CloudScanListener {
    // Same CloudScanListener implementation as above
})
```

#### AppInfo Conversion:
```kotlin
private fun convertAppInfo(appInfo: AppInfo): ThreatInfo? {
    return try {
        // Only convert if it's actually a threat (score >= 6)
        if (appInfo.score >= 6) {
            ThreatInfo(
                id = appInfo.packageName ?: appInfo.apkPath ?: appInfo.md5,
                appName = appInfo.appName ?: "Unknown",
                packageName = appInfo.packageName,
                filePath = appInfo.apkPath,
                md5 = appInfo.md5 ?: "",
                score = appInfo.score,
                virusName = appInfo.virusName ?: "",
                category = appInfo.category ?: "",
                summary = appInfo.summary ?: emptyArray(),
                isFromStatic = appInfo.isFromStatic
            )
        } else {
            null // Safe apps are not threats
        }
    } catch (e: Exception) {
        Log.e(TAG, "Error converting AppInfo", e)
        null
    }
}
```

### 3. Remove Simulation Code
After integrating the real SDK, remove these simulation methods from `CloudScanManager.kt`:
- `simulateQuickScan()`
- `simulateFolderScan()`
- `simulateComprehensiveScan()`
- `createMockThreats()`

And replace the simulation calls with real SDK calls in the scan methods.

## Testing

### 1. Test with Real SDK
1. Add the actual CloudScan SDK files to `app/libs`
2. Uncomment the SDK integration code
3. Remove simulation code
4. Test all three scan types:
   - Quick Scan
   - Folder Scan
   - Comprehensive Scan

### 2. Verify Functionality
- Scan progress updates correctly
- Threats are detected and displayed
- Ignore functionality works
- Delete functionality works
- Error handling works properly

## Current Implementation Status

✅ **Completed:**
- UI components for scan progress and results
- Database structure for ignored threats
- ViewModel with SDK integration points
- Flow-based architecture for scan progress
- Error handling and cancellation support

🔄 **Ready for SDK Integration:**
- CloudScanManager wrapper class with commented SDK code
- All integration points identified and prepared
- Simulation code ready to be replaced

⏳ **Pending SDK Files:**
- Actual CloudScan SDK (.aar or .jar files)
- SDK documentation for any additional setup requirements

## Notes

1. The current implementation uses simulation data for testing purposes
2. All SDK integration points are clearly marked with TODO comments
3. The architecture is designed to easily switch from simulation to real SDK
4. Error handling and edge cases are already implemented
5. The UI will work seamlessly once the real SDK is integrated

## Region Configuration

Choose the appropriate region based on your target market:
- `Region.INTL` - International markets
- `Region.BAIDU` - China market

## Troubleshooting

If you encounter issues after SDK integration:

1. **Build Errors:** Ensure all SDK dependencies are included
2. **Runtime Errors:** Check that all required permissions are granted
3. **Network Issues:** Verify internet connectivity and firewall settings
4. **Scan Failures:** Check SDK logs for detailed error information

## Support

For SDK-specific issues, refer to the Trustlook CloudScan SDK documentation or contact Trustlook support.
