# Android Cleaning Application Navigation Structure

## Activities and Navigation Structure

The application will consist of the following activities and navigation components:

### 1. Splash Activity (`SplashActivity`)
- Initial loading screen
- App initialization
- Transitions to Main Activity after initialization

### 2. Main Activity (`MainActivity`)
Contains the following navigation destinations:

#### Bottom Navigation Tabs
1. **Files Tab**
   - Dashboard for file management features
   - Quick access to file cleaning tools
   - Storage usage visualization

2. **Antivirus Tab**
   - Virus scanning dashboard
   - Security status overview
   - Quick scan button

3. **More Tab**
   - Additional features and tools
   - Access to system information
   - Access to application management

#### Settings Screen
- Accessible via settings icon in the app bar
- Contains application preferences and configurations
- Theme settings, notification preferences, etc.

### 3. File Browser Activity (`FileBrowserActivity`)
A unified file browsing activity with different modes:

#### Media File Browsers (Shared UI with specific adaptations)
- **Images Browser**: Gallery view with image-specific features
- **Videos Browser**: Gallery view with video-specific features

#### Document File Browsers (Shared UI)
- **Audios Browser**: List view of audio files
- **Documents Browser**: List view of document files
- **Archives Browser**: List view of ZIP and other archive files
- **APKs Browser**: List view of APK installation files

#### Storage Browser
- File system navigation
- Storage device selection
- Folder structure visualization

### 4. File Utility Activity (`FileUtilityActivity`)
Contains various file management utilities:

- **Large Files**: Identification and management of large files
- **Recent Files**: Recently created or modified files
- **Duplicate Files**: Detection and management of duplicate files
- **Redundant Files**: Identification of unnecessary files
- **Similar Photos**: Detection and management of similar images

### 5. System Info Activity (`SystemInfoActivity`)
Displays system information and monitoring tools:

- **RAM Usage**: Memory usage monitoring and optimization
- **Battery Info**: Battery status and optimization
- **CPU Monitor**: CPU usage monitoring
- **App Details**: Detailed information about installed apps
- **Network**: Network usage and status monitoring

### 6. App Info Activity (`AppInfoActivity`)
Application management tools:

- **App Manager**: Installation, uninstallation, and app details
- **App Process**: Running processes and background apps management

### 7. Virus Scan Activity (`VirusScanActivity`)
Antivirus scanning functionality:

- **Scanning Animation**: Visual feedback during scanning process
- **Ignore List**: Management of ignored threats
- **Scan Complete**: Results and actions after scan completion

### 8. Operation Complete Activity (`OperationCompleteActivity`)
Confirmation and summary screen after completing operations:

- Operation results summary
- Statistics (space freed, threats removed, etc.)
- Recommendations for further actions

## Navigation Implementation

### Navigation Components
- Use Jetpack Navigation Component for in-activity navigation
- Implement custom transitions between destinations
- Use shared element transitions where appropriate

### Navigation Patterns
- Use bottom navigation for main sections
- Use drawer navigation for secondary features
- Implement back stack handling for intuitive navigation

### Deep Linking
- Support deep links to specific features
- Enable notification-based navigation
- Support external app launching of specific features

## UI Component Reuse Strategy

### Reusable Screens
1. **Media Browser Template**
   - Reused for Images and Videos browsers
   - Customizable grid layout
   - Media-specific actions

2. **Document Browser Template**
   - Reused for Audios, Documents, Archives, and APKs browsers
   - Customizable list layout
   - Document-specific actions

3. **Scan Template**
   - Reused for virus scanning and file scanning
   - Progress visualization
   - Results display

4. **Information Display Template**
   - Reused for system info and app info displays
   - Metric visualization
   - Action buttons

### Navigation Reuse
- Create reusable navigation graphs for similar features
- Implement navigation action extensions for common navigation patterns
- Use navigation arguments for customizing shared destinations

## Screen Transitions and Animations

### Transition Types
- Use material motion patterns for transitions
- Implement shared element transitions for media browsing
- Use container transforms for expanding UI elements

### Animation Guidelines
- Keep animations under 300ms for responsiveness
- Use physics-based animations for natural feel
- Ensure animations support accessibility settings

## Navigation State Management

### State Preservation
- Preserve navigation state across configuration changes
- Implement proper back stack management
- Save and restore scroll position and UI state

### Deep Linking State
- Handle deep link navigation state properly
- Support returning to previous state after deep link navigation
- Maintain proper back stack when navigating from notifications
