# Data Storage Guidelines

## Overview
This document outlines the guidelines for data storage in the Android cleaning application. It provides recommendations for when to use MMKV for simple data storage and when to use Room for database operations.

## MMKV for Simple Data Storage

### What is MMKV?
MMKV is an efficient, small, and easy-to-use mobile key-value storage framework developed by WeChat. It uses memory mapping for high-performance I/O and protobuf for efficient serialization/deserialization.

### When to Use MMKV
Use MMKV for:
- User preferences and settings
- Small data objects that don't require complex queries
- Frequently accessed data that needs to be persisted
- Session data
- Feature flags and configuration

### Implementation Guidelines

#### Initialization
Initialize MMKV in the Application class:

```kotlin
class CleanApp : Application() {
    override fun onCreate() {
        super.onCreate()
        MMKV.initialize(this)
    }
}
```

#### Creating MMKV Instances
Create separate MMKV instances for different data categories:

```kotlin
// For user preferences
val userPrefs = MMKV.mmkvWithID("user_preferences")

// For app settings
val appSettings = MMKV.mmkvWithID("app_settings")

// For feature flags
val featureFlags = MMKV.mmkvWithID("feature_flags")
```

#### Data Access Wrapper
Create a wrapper class for each data category:

```kotlin
class UserPreferencesManager(private val mmkv: MMKV) {
    fun setDarkMode(enabled: Boolean) {
        mmkv.encode("dark_mode", enabled)
    }
    
    fun isDarkModeEnabled(): Boolean {
        return mmkv.decodeBool("dark_mode", false)
    }
    
    // Other preference methods
}
```

## Room for Database Storage

### What is Room?
Room is a persistence library that provides an abstraction layer over SQLite. It allows for more robust database access while harnessing the full power of SQLite.

### When to Use Room
Use Room for:
- Complex data models with relationships
- Data that needs to be queried with SQL
- Large datasets
- Data that requires transactions
- Data that benefits from database constraints

### Implementation Guidelines

#### Entity Definition
Define entities that represent tables in the database:

```kotlin
@Entity(tableName = "junk_files")
data class JunkFileEntity(
    @PrimaryKey val id: String,
    val path: String,
    val size: Long,
    val type: String,
    val lastModified: Long,
    val isSelected: Boolean = false
)
```

#### DAO (Data Access Object)
Create DAOs to define database operations:

```kotlin
@Dao
interface JunkFileDao {
    @Query("SELECT * FROM junk_files")
    fun getAllJunkFiles(): Flow<List<JunkFileEntity>>
    
    @Query("SELECT * FROM junk_files WHERE type = :type")
    fun getJunkFilesByType(type: String): Flow<List<JunkFileEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertJunkFiles(junkFiles: List<JunkFileEntity>)
    
    @Delete
    suspend fun deleteJunkFiles(junkFiles: List<JunkFileEntity>)
    
    @Query("DELETE FROM junk_files")
    suspend fun clearJunkFiles()
}
```

#### Database Definition
Define the database:

```kotlin
@Database(
    entities = [JunkFileEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun junkFileDao(): JunkFileDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getInstance(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "clean_app_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
```

#### Repository Implementation
Implement repositories that use the DAOs:

```kotlin
class JunkFileRepositoryImpl(
    private val junkFileDao: JunkFileDao
) : JunkFileRepository {
    override fun getAllJunkFiles(): Flow<List<JunkFile>> {
        return junkFileDao.getAllJunkFiles().map { entities ->
            entities.map { it.toDomain() }
        }
    }
    
    override fun getJunkFilesByType(type: String): Flow<List<JunkFile>> {
        return junkFileDao.getJunkFilesByType(type).map { entities ->
            entities.map { it.toDomain() }
        }
    }
    
    override suspend fun saveJunkFiles(junkFiles: List<JunkFile>) {
        junkFileDao.insertJunkFiles(junkFiles.map { it.toEntity() })
    }
    
    override suspend fun deleteJunkFiles(junkFiles: List<JunkFile>) {
        junkFileDao.deleteJunkFiles(junkFiles.map { it.toEntity() })
    }
    
    override suspend fun clearJunkFiles() {
        junkFileDao.clearJunkFiles()
    }
}
```

## Best Practices

### Data Separation
- Keep different types of data in separate storage mechanisms
- Use MMKV for preferences and settings
- Use Room for structured data

### Data Migration
- Plan for data migration between versions
- Implement migration strategies for Room databases
- Consider backward compatibility

### Security
- Encrypt sensitive data
- Use encrypted versions of MMKV for sensitive information
- Consider using Room with SQLCipher for database encryption

### Performance
- Avoid storing large blobs in MMKV
- Use Room for large datasets
- Implement pagination for large data queries
- Use transactions for batch operations

### Testing
- Create test implementations of repositories
- Mock data sources for unit testing
- Use in-memory databases for testing Room implementations
