# Code Reuse Strategies for Android Cleaning Application

## 1. Common UI Components Library

### Reusable UI Components
Create a comprehensive library of reusable UI components that can be used across different features:

```kotlin
// Example of a reusable card component
@Composable
fun StatusCard(
    title: String,
    value: String,
    icon: @Composable () -> Unit,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = contentColorFor(backgroundColor),
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor,
            contentColor = contentColor
        ),
        modifier = modifier.then(
            if (onClick != null) Modifier.clickable { onClick() } else Modifier
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = contentColor.copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                CompositionLocalProvider(
                    LocalContentColor provides contentColor
                ) {
                    icon()
                }
            }
            Spacer(modifier = Modifier.width(16.dp))
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = value,
                    style = MaterialTheme.typography.titleLarge
                )
            }
        }
    }
}
```

### Component Catalog
Maintain a component catalog with previews for all reusable components:

```kotlin
@Preview(showBackground = true)
@Composable
fun StatusCardPreview() {
    Clean0522Theme {
        StatusCard(
            title = "Storage Used",
            value = "45.2 GB",
            icon = { Icon(Icons.Default.Storage, contentDescription = null) }
        )
    }
}
```

## 2. Extension Functions

### File Size Formatting
```kotlin
fun Long.formatFileSize(): String {
    if (this <= 0) return "0 B"
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    val digitGroups = (log10(this.toDouble()) / log10(1024.0)).toInt()
    return DecimalFormat("#,##0.#").format(this / 1024.0.pow(digitGroups.toDouble())) + " " + units[digitGroups]
}
```

### Date Formatting
```kotlin
fun Long.formatDate(pattern: String = "MMM dd, yyyy"): String {
    val date = Date(this)
    val formatter = SimpleDateFormat(pattern, Locale.getDefault())
    return formatter.format(date)
}
```

### Context Extensions
```kotlin
fun Context.getAvailableStorage(): Long {
    val stat = StatFs(Environment.getExternalStorageDirectory().path)
    return stat.availableBlocksLong * stat.blockSizeLong
}

fun Context.getTotalStorage(): Long {
    val stat = StatFs(Environment.getExternalStorageDirectory().path)
    return stat.totalBlocksLong * stat.blockSizeLong
}
```

## 3. Base Classes

### Base Repository
```kotlin
interface BaseRepository<T> {
    suspend fun getAll(): Flow<List<T>>
    suspend fun getById(id: String): Flow<T?>
    suspend fun insert(item: T): Result<Unit>
    suspend fun update(item: T): Result<Unit>
    suspend fun delete(item: T): Result<Unit>
}
```

### Base ViewModel
```kotlin
abstract class BaseViewModel<S, E>(initialState: S) : ViewModel() {
    private val _state = MutableStateFlow(initialState)
    val state: StateFlow<S> = _state.asStateFlow()
    
    protected fun updateState(update: (S) -> S) {
        _state.update(update)
    }
    
    abstract fun handleEvent(event: E)
}
```

### Base Screen
```kotlin
@Composable
fun <S, E> BaseScreen(
    viewModel: BaseViewModel<S, E>,
    content: @Composable (state: S, onEvent: (E) -> Unit) -> Unit
) {
    val state by viewModel.state.collectAsState()
    content(state) { event ->
        viewModel.handleEvent(event)
    }
}
```

## 4. Utility Classes

### File Utilities
```kotlin
object FileUtils {
    fun getJunkFiles(context: Context): Flow<List<JunkFile>> = flow {
        // Implementation
    }
    
    fun deleteFiles(files: List<File>): Result<Long> {
        // Implementation
    }
    
    fun findDuplicates(directory: File): Flow<List<List<File>>> = flow {
        // Implementation
    }
}
```

### Permission Utilities
```kotlin
object PermissionUtils {
    fun hasStoragePermission(context: Context): Boolean {
        // Implementation
    }
    
    fun requestStoragePermission(activity: Activity) {
        // Implementation
    }
    
    fun hasUsageStatsPermission(context: Context): Boolean {
        // Implementation
    }
}
```

## 5. Generic Algorithms

### Generic File Scanner
```kotlin
class FileScanner(private val context: Context) {
    fun scan(
        directory: File,
        filter: (File) -> Boolean,
        onProgress: (scannedCount: Int, totalSize: Long) -> Unit
    ): Flow<List<File>> = flow {
        // Implementation
    }
}
```

### Generic List Adapter
```kotlin
class GenericListAdapter<T>(
    private val diffCallback: DiffUtil.ItemCallback<T>,
    private val itemContent: @Composable (item: T) -> Unit
) {
    @Composable
    fun LazyList(
        items: List<T>,
        modifier: Modifier = Modifier
    ) {
        LazyColumn(modifier = modifier) {
            items(
                items = items,
                key = { item -> item.hashCode() }
            ) { item ->
                itemContent(item)
            }
        }
    }
}
```

## 6. State Management

### Generic UI State
```kotlin
sealed interface UiState<out T> {
    object Loading : UiState<Nothing>
    data class Success<T>(val data: T) : UiState<T>
    data class Error(val message: String) : UiState<Nothing>
}
```

### State Collectors
```kotlin
@Composable
fun <T> UiState<T>.Handle(
    onLoading: @Composable () -> Unit = { LoadingIndicator() },
    onError: @Composable (message: String) -> Unit = { ErrorMessage(it) },
    onSuccess: @Composable (data: T) -> Unit
) {
    when (this) {
        is UiState.Loading -> onLoading()
        is UiState.Error -> onError(message)
        is UiState.Success -> onSuccess(data)
    }
}
```

## 7. Feature Templates

### Scanner Feature Template
```kotlin
// Generic scanner feature that can be reused for different scanning operations
@Composable
fun <T> ScannerFeature(
    title: String,
    description: String,
    scannerState: UiState<List<T>>,
    onScanClick: () -> Unit,
    onItemClick: (T) -> Unit,
    itemContent: @Composable (T) -> Unit,
    emptyContent: @Composable () -> Unit = { EmptyState() },
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.headlineMedium
        )
        Text(
            text = description,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = onScanClick) {
            Text("Start Scan")
        }
        Spacer(modifier = Modifier.height(16.dp))
        scannerState.Handle(
            onSuccess = { items ->
                if (items.isEmpty()) {
                    emptyContent()
                } else {
                    LazyColumn {
                        items(items) { item ->
                            Box(modifier = Modifier.clickable { onItemClick(item) }) {
                                itemContent(item)
                            }
                        }
                    }
                }
            }
        )
    }
}
```

## 8. Documentation Templates

### Feature Documentation Template
```markdown
# Feature Name

## Purpose
Brief description of what this feature does and why it's valuable.

## Components
- Component 1: Description
- Component 2: Description

## Usage
```kotlin
// Example code showing how to use this feature
```

## Dependencies
- Dependency 1
- Dependency 2

## Testing
How to test this feature.
```

## 9. Test Templates

### Use Case Test Template
```kotlin
class SomeUseCaseTest {
    @get:Rule
    val coroutineRule = MainCoroutineRule()
    
    private lateinit var repository: SomeRepository
    private lateinit var useCase: SomeUseCase
    
    @Before
    fun setup() {
        repository = mockk()
        useCase = SomeUseCase(repository)
    }
    
    @Test
    fun `when condition then expected result`() = runTest {
        // Arrange
        coEvery { repository.someMethod() } returns expectedValue
        
        // Act
        val result = useCase.execute()
        
        // Assert
        assertEquals(expectedValue, result)
        coVerify { repository.someMethod() }
    }
}
```
