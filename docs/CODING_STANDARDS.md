# Coding Standards for Android Cleaning Application

## Code Organization and Reusability

### 1. DRY (Don't Repeat Yourself) Principle
- Extract common functionality into reusable components
- Create utility classes for frequently used operations
- Use extension functions for adding functionality to existing classes
- Implement base classes for similar components

### 2. Component Reusability
- Create generic UI components that can be reused across features
- Parameterize components to make them adaptable to different contexts
- Document component usage with examples
- Use composition over inheritance

### 3. Function Design
- Keep functions small and focused on a single task
- Limit function parameters (max 3-4 parameters)
- Use named parameters for clarity
- Return meaningful values instead of modifying state

### 4. Extension Functions
- Create extension functions for common operations on standard types
- Group related extension functions in dedicated files
- Name extension functions clearly to indicate their purpose
- Document extension functions with KDoc

## Kotlin Best Practices

### 1. Null Safety
- Use non-nullable types whenever possible
- Handle nullable types explicitly with safe calls (`?.`) or Elvis operator (`?:`)
- Avoid using `!!` operator except in tests
- Use `requireNotNull()` or `checkNotNull()` for preconditions

### 2. Collections
- Use immutable collections (`listOf`, `setOf`, `mapOf`) by default
- Use collection operations (map, filter, etc.) instead of loops when appropriate
- Use sequences for large collections to improve performance
- Use scope functions (`let`, `apply`, `run`, `with`, `also`) appropriately

### 3. Coroutines
- Use structured concurrency with coroutine scopes
- Handle exceptions properly in coroutines
- Use appropriate dispatchers for different types of work
- Use Flow for reactive streams

### 4. Property Delegation
- Use `by lazy` for lazy initialization
- Use `by Delegates.observable` for observing property changes
- Use `by viewModels()` for ViewModel initialization
- Create custom property delegates for common patterns

## Compose UI Guidelines

### 1. Composable Function Structure
- Keep composable functions small and focused
- Extract reusable UI components
- Use parameters to make composables flexible
- Follow the naming convention: noun for UI elements, verb for actions

```kotlin
// Good
@Composable
fun CleaningCard(
    title: String,
    description: String,
    progress: Float,
    onCleanClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Implementation
}

// Bad - Too many responsibilities
@Composable
fun CleaningSection(
    junkFiles: List<JunkFile>,
    isScanning: Boolean,
    // Many more parameters
) {
    // Complex implementation with multiple responsibilities
}
```

### 2. State Management
- Use `remember` and `mutableStateOf` for local UI state
- Use ViewModels for complex state management
- Use `collectAsState()` to collect Flow as state
- Define clear UI state classes for each screen

```kotlin
// UI State class
data class FileCleaningState(
    val junkFiles: List<JunkFile> = emptyList(),
    val isScanning: Boolean = false,
    val scanProgress: Float = 0f,
    val isCleaning: Boolean = false,
    val cleaningProgress: Float = 0f,
    val error: String? = null
)

// ViewModel
class FileCleaningViewModel(
    private val scanJunkFilesUseCase: ScanJunkFilesUseCase,
    private val cleanJunkFilesUseCase: CleanJunkFilesUseCase
) : ViewModel() {
    private val _state = MutableStateFlow(FileCleaningState())
    val state: StateFlow<FileCleaningState> = _state.asStateFlow()
    
    // Implementation
}

// Composable
@Composable
fun FileCleaningScreen(viewModel: FileCleaningViewModel = hiltViewModel()) {
    val state by viewModel.state.collectAsState()
    
    // UI implementation using state
}
```

### 3. Reusable UI Components
- Create a library of reusable UI components
- Use consistent styling and theming
- Implement preview functions for all components
- Document component usage

```kotlin
@Composable
fun ActionButton(
    text: String,
    onClick: () -> Unit,
    icon: @Composable () -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    // Implementation
}

@Preview(showBackground = true)
@Composable
fun ActionButtonPreview() {
    Clean0522Theme {
        ActionButton(
            text = "Clean Now",
            onClick = {},
            icon = { Icon(Icons.Default.Delete, contentDescription = null) }
        )
    }
}
```

## Error Handling

### 1. Exception Handling
- Use sealed classes for representing errors
- Handle exceptions at appropriate levels
- Provide meaningful error messages
- Log errors with appropriate severity

```kotlin
sealed class CleaningError {
    object PermissionDenied : CleaningError()
    object InsufficientStorage : CleaningError()
    data class FileSystemError(val message: String) : CleaningError()
    data class UnknownError(val throwable: Throwable) : CleaningError()
}

// Usage in repository
suspend fun cleanJunkFiles(): Result<Long> {
    return try {
        // Cleaning implementation
        Result.success(bytesFreed)
    } catch (e: SecurityException) {
        Result.failure(CleaningError.PermissionDenied)
    } catch (e: IOException) {
        Result.failure(CleaningError.FileSystemError(e.message ?: "Unknown IO error"))
    } catch (e: Exception) {
        Result.failure(CleaningError.UnknownError(e))
    }
}
```

### 2. UI Error Handling
- Display user-friendly error messages
- Provide recovery actions when possible
- Use consistent error UI components
- Handle loading, success, and error states

## Documentation

### 1. Code Documentation
- Use KDoc for all public APIs
- Document parameters, return values, and exceptions
- Provide usage examples for complex functions
- Document non-obvious implementation details

### 2. README Files
- Create README.md files for each feature module
- Document architecture decisions
- Provide setup instructions
- Include usage examples

## Testing

### 1. Unit Testing
- Write tests for all use cases
- Test repository implementations
- Use mockk for mocking dependencies
- Follow AAA (Arrange-Act-Assert) pattern

### 2. UI Testing
- Test key user flows
- Verify UI components render correctly
- Test different screen sizes and configurations
- Use Compose testing libraries
