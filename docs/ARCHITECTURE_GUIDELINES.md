# Android Cleaning Application Architecture Guidelines

## Project Overview
This document outlines the architecture, coding standards, and best practices for the Android cleaning application developed with Jetpack Compose and Kotlin.

## Architecture

### Clean Architecture
The application follows Clean Architecture principles with the following layers:

1. **Presentation Layer**
   - UI components (Compose)
   - ViewModels
   - UI States and Events

2. **Domain Layer**
   - Use Cases
   - Domain Models
   - Repository Interfaces

3. **Data Layer**
   - Repository Implementations
   - Data Sources (Local and Remote)
   - Data Models (DTOs)

### Data Storage Guidelines
1. **Simple Data Storage**
   - Use MMKV for storing simple key-value pairs
   - Appropriate for preferences, settings, and small data objects
   - Provides better performance than SharedPreferences

2. **Complex Data Storage**
   - Use Room database for structured data that requires queries
   - Appropriate for large datasets, complex relationships, and data that needs to be queried
   - Implement DAO pattern for database access

### Package Structure
```
com.example.clean0522/
├── di/                  # Dependency Injection modules
├── domain/              # Domain layer
│   ├── model/           # Domain models
│   ├── repository/      # Repository interfaces
│   └── usecase/         # Use cases
├── data/                # Data layer
│   ├── local/           # Local data sources
│   │   ├── dao/         # Room DAOs
│   │   ├── db/          # Database configuration
│   │   └── model/       # Local data models
│   ├── remote/          # Remote data sources (if applicable)
│   │   ├── api/         # API services
│   │   └── model/       # Remote data models
│   └── repository/      # Repository implementations
├── ui/                  # Presentation layer
│   ├── theme/           # App theme
│   ├── common/          # Common UI components
│   └── features/        # Feature-specific UI components
│       ├── dashboard/   # Dashboard feature
│       ├── analysis/    # Analysis feature
│       ├── cleaning/    # Cleaning feature
│       └── settings/    # Settings feature
└── util/                # Utility classes
```

## Coding Standards

### General Principles
1. **Single Responsibility Principle**: Each class should have only one reason to change.
2. **Dependency Inversion**: High-level modules should not depend on low-level modules.
3. **Interface Segregation**: Clients should not be forced to depend on methods they do not use.
4. **Code Reusability**: Extract common functionality into reusable components.
5. **Testability**: Design code to be easily testable.

### Kotlin Specific
1. Use Kotlin's language features appropriately:
   - Extension functions for adding functionality to existing classes
   - Data classes for models
   - Sealed classes for representing restricted class hierarchies
   - Coroutines for asynchronous operations
   - Flow for reactive programming

2. Naming conventions:
   - Use camelCase for variables and functions
   - Use PascalCase for classes and interfaces
   - Use UPPER_SNAKE_CASE for constants

### Compose UI Guidelines
1. **Composable Functions**:
   - Keep composable functions small and focused
   - Extract reusable UI components
   - Use preview annotations for UI development

2. **State Management**:
   - Use `remember` and `mutableStateOf` for local UI state
   - Use ViewModels for complex state management
   - Use `collectAsState()` to collect Flow as state

3. **UI Components**:
   - Create reusable UI components in the `common` package
   - Follow Material Design guidelines
   - Support both light and dark themes

## Feature-Specific Guidelines

### Cleaning Features
1. **File Scanning**:
   - Implement efficient file scanning algorithms
   - Use coroutines for background processing
   - Show progress indicators during scanning

2. **Junk Detection**:
   - Create clear categorization of junk files
   - Implement safe detection algorithms
   - Allow user customization of detection rules

3. **Cleaning Operations**:
   - Implement safe deletion mechanisms
   - Provide undo functionality when possible
   - Show clear progress and results

4. **Memory Optimization**:
   - Implement memory usage analysis
   - Provide app-specific optimization recommendations
   - Show before/after comparisons

## Testing Strategy
1. **Unit Tests**:
   - Test all use cases
   - Test repository implementations
   - Use mockk for mocking dependencies

2. **UI Tests**:
   - Test key user flows
   - Verify UI components render correctly
   - Use Compose testing libraries

3. **Integration Tests**:
   - Test interactions between layers
   - Verify data flow through the application

## Documentation Requirements
1. Each feature should have a dedicated README.md file explaining:
   - Purpose and functionality
   - Key components
   - Usage examples

2. All public APIs should have KDoc comments

3. Complex algorithms should include explanatory comments

## Performance Considerations
1. Minimize main thread operations
2. Use appropriate data structures
3. Implement pagination for large data sets
4. Cache results when appropriate
5. Use lazy loading for UI elements

## Security Guidelines
1. Request only necessary permissions
2. Securely handle user data
3. Implement proper error handling
4. Follow Android security best practices

## Accessibility
1. Support different screen sizes
2. Implement content descriptions for all UI elements
3. Support system font size changes
4. Test with TalkBack
